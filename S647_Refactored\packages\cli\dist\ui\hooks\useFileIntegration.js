/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import { useState, useCallback, useEffect } from 'react';
import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';
/**
 * File integration hook
 */
export function useFileIntegration(config, logger) {
    const [recentFiles, setRecentFiles] = useState([]);
    const [fileCache, setFileCache] = useState(new Map());
    const [isLoading, setIsLoading] = useState(false);
    // Get allowed file extensions
    const getAllowedExtensions = useCallback(() => {
        const extensions = config.tools.file?.allowedExtensions || [];
        return Array.isArray(extensions) ? extensions : extensions.split(',');
    }, [config.tools.file?.allowedExtensions]);
    // Check if file is allowed
    const isFileAllowed = useCallback((filePath) => {
        const allowedExtensions = getAllowedExtensions();
        if (allowedExtensions.length === 0)
            return true;
        const ext = path.extname(filePath).toLowerCase();
        return allowedExtensions.includes(ext);
    }, [getAllowedExtensions]);
    // Check if file size is within limits
    const isFileSizeAllowed = useCallback((size) => {
        const maxSize = config.tools.file?.maxSize || 10485760; // 10MB default
        return size <= maxSize;
    }, [config.tools.file?.maxSize]);
    // Detect file type
    const detectFileType = useCallback((filePath, content) => {
        const ext = path.extname(filePath).toLowerCase();
        // Image files
        if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'].includes(ext)) {
            return 'image';
        }
        // Known text files
        if (['.txt', '.md', '.js', '.ts', '.tsx', '.jsx', '.py', '.java', '.cpp', '.c', '.h',
            '.css', '.html', '.json', '.yaml', '.yml', '.xml', '.sql', '.sh', '.bat', '.ps1',
            '.rs', '.go', '.php', '.rb', '.swift', '.kt', '.dart', '.vue', '.svelte'].includes(ext)) {
            return 'text';
        }
        // Check content if available
        if (content) {
            // Simple binary detection - check for null bytes
            for (let i = 0; i < Math.min(content.length, 1024); i++) {
                if (content[i] === 0) {
                    return 'binary';
                }
            }
            return 'text';
        }
        return 'binary';
    }, []);
    // Read file content
    const readFileContent = useCallback(async (filePath) => {
        try {
            const absolutePath = path.resolve(filePath);
            // Check if file exists
            if (!fs.existsSync(absolutePath)) {
                throw new Error(`File not found: ${filePath}`);
            }
            const stats = fs.statSync(absolutePath);
            // Check if it's a file
            if (!stats.isFile()) {
                throw new Error(`Path is not a file: ${filePath}`);
            }
            // Check file size
            if (!isFileSizeAllowed(stats.size)) {
                throw new Error(`File too large: ${filePath} (${stats.size} bytes)`);
            }
            // Check if file is allowed
            if (!isFileAllowed(filePath)) {
                throw new Error(`File type not allowed: ${filePath}`);
            }
            // Read file content
            const buffer = fs.readFileSync(absolutePath);
            const fileType = detectFileType(filePath, buffer);
            let content;
            let encoding = 'utf8';
            if (fileType === 'image') {
                // For images, provide metadata instead of content
                content = `[Image file: ${path.basename(filePath)}, Size: ${stats.size} bytes, Type: ${path.extname(filePath)}]`;
                encoding = 'binary';
            }
            else if (fileType === 'binary') {
                // For binary files, provide metadata
                content = `[Binary file: ${path.basename(filePath)}, Size: ${stats.size} bytes]`;
                encoding = 'binary';
            }
            else {
                // For text files, read as UTF-8
                content = buffer.toString('utf8');
                // Truncate very long files
                const maxLength = 50000; // ~50KB of text
                if (content.length > maxLength) {
                    content = content.substring(0, maxLength) + '\n\n[... file truncated ...]';
                }
            }
            const fileRef = {
                path: absolutePath,
                content,
                size: stats.size,
                lastModified: stats.mtime,
                encoding,
                type: fileType,
            };
            // Cache the file reference
            setFileCache(prev => new Map(prev).set(absolutePath, fileRef));
            // Add to recent files
            setRecentFiles(prev => {
                const newRecent = [absolutePath, ...prev.filter(p => p !== absolutePath)];
                return newRecent.slice(0, 20); // Keep last 20 files
            });
            logger.debug(`File read successfully: ${filePath}`);
            return fileRef;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger.error(`Failed to read file ${filePath}:`, errorMessage);
            return {
                path: filePath,
                content: '',
                size: 0,
                lastModified: new Date(),
                encoding: 'utf8',
                type: 'text',
                error: errorMessage,
            };
        }
    }, [isFileAllowed, isFileSizeAllowed, detectFileType, logger]);
    // Get file suggestions based on partial path
    const getFileSuggestions = useCallback(async (partialPath) => {
        try {
            setIsLoading(true);
            const suggestions = [];
            const cwd = process.cwd();
            // If partial path is empty, suggest common files and recent files
            if (!partialPath) {
                // Add recent files
                for (const recentFile of recentFiles.slice(0, 5)) {
                    if (fs.existsSync(recentFile)) {
                        const stats = fs.statSync(recentFile);
                        suggestions.push({
                            path: path.relative(cwd, recentFile),
                            type: 'file',
                            size: stats.size,
                            lastModified: stats.mtime,
                        });
                    }
                }
                // Add common files in current directory
                const commonFiles = ['package.json', 'README.md', 'tsconfig.json', '.env', '.gitignore'];
                for (const file of commonFiles) {
                    const fullPath = path.join(cwd, file);
                    if (fs.existsSync(fullPath) && !suggestions.some(s => s.path === file)) {
                        const stats = fs.statSync(fullPath);
                        suggestions.push({
                            path: file,
                            type: 'file',
                            size: stats.size,
                            lastModified: stats.mtime,
                        });
                    }
                }
                return suggestions.slice(0, 10);
            }
            // Resolve partial path
            const searchPath = path.resolve(cwd, partialPath);
            const searchDir = path.dirname(searchPath);
            const searchPattern = path.basename(searchPath);
            // Check if search directory exists
            if (!fs.existsSync(searchDir)) {
                return suggestions;
            }
            // Get directory contents
            const entries = fs.readdirSync(searchDir, { withFileTypes: true });
            for (const entry of entries) {
                const entryName = entry.name;
                const entryPath = path.join(searchDir, entryName);
                const relativePath = path.relative(cwd, entryPath);
                // Skip hidden files and node_modules
                if (entryName.startsWith('.') || entryName === 'node_modules') {
                    continue;
                }
                // Check if entry matches search pattern
                if (searchPattern && !entryName.toLowerCase().includes(searchPattern.toLowerCase())) {
                    continue;
                }
                try {
                    const stats = fs.statSync(entryPath);
                    if (entry.isDirectory()) {
                        suggestions.push({
                            path: relativePath + '/',
                            type: 'directory',
                            lastModified: stats.mtime,
                        });
                    }
                    else if (entry.isFile()) {
                        // Only suggest allowed file types
                        if (isFileAllowed(entryPath)) {
                            suggestions.push({
                                path: relativePath,
                                type: 'file',
                                size: stats.size,
                                lastModified: stats.mtime,
                            });
                        }
                    }
                }
                catch (error) {
                    // Skip files we can't stat
                    continue;
                }
            }
            // Sort suggestions: directories first, then by name
            suggestions.sort((a, b) => {
                if (a.type !== b.type) {
                    return a.type === 'directory' ? -1 : 1;
                }
                return a.path.localeCompare(b.path);
            });
            return suggestions.slice(0, 10);
        }
        catch (error) {
            logger.error('Failed to get file suggestions:', error);
            return [];
        }
        finally {
            setIsLoading(false);
        }
    }, [recentFiles, isFileAllowed, logger]);
    // Process text with file references
    const processFileReferences = useCallback(async (text) => {
        if (!config.tools.file?.enabled) {
            return text;
        }
        // Find all @file references
        const fileRefRegex = /@([^\s@]+)/g;
        const matches = Array.from(text.matchAll(fileRefRegex));
        if (matches.length === 0) {
            return text;
        }
        let processedText = text;
        for (const match of matches) {
            const fullMatch = match[0];
            const filePath = match[1];
            if (!filePath)
                continue;
            try {
                const fileRef = await readFileContent(filePath);
                if (fileRef.error) {
                    // Replace with error message
                    const replacement = `[Error reading ${filePath}: ${fileRef.error}]`;
                    processedText = processedText.replace(fullMatch, replacement);
                }
                else {
                    // Replace with file content
                    const replacement = `\n\n--- File: ${fileRef.path} ---\n${fileRef.content}\n--- End of ${path.basename(fileRef.path)} ---\n\n`;
                    processedText = processedText.replace(fullMatch, replacement);
                }
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                const replacement = `[Error reading ${filePath}: ${errorMessage}]`;
                processedText = processedText.replace(fullMatch, replacement);
            }
        }
        return processedText;
    }, [config.tools.file?.enabled, readFileContent]);
    // Clear file cache
    const clearCache = useCallback(() => {
        setFileCache(new Map());
        logger.debug('File cache cleared');
    }, [logger]);
    // Get cache statistics
    const getCacheStats = useCallback(() => {
        const totalSize = Array.from(fileCache.values()).reduce((sum, ref) => sum + ref.size, 0);
        return {
            fileCount: fileCache.size,
            totalSize,
            recentFilesCount: recentFiles.length,
        };
    }, [fileCache, recentFiles]);
    return {
        readFileContent,
        getFileSuggestions,
        processFileReferences,
        clearCache,
        getCacheStats,
        recentFiles,
        isLoading,
        isFileAllowed,
        isFileSizeAllowed,
    };
}
//# sourceMappingURL=useFileIntegration.js.map