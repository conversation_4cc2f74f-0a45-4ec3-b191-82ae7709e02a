/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { Configuration, Logger } from '@inkbytefo/s647-shared';
/**
 * Streaming states
 */
export declare enum StreamingState {
    Idle = "idle",
    Connecting = "connecting",
    Streaming = "streaming",
    Complete = "complete",
    Error = "error",
    UserCancelled = "cancelled"
}
/**
 * Message types
 */
export declare enum MessageType {
    User = "user",
    Assistant = "assistant",
    System = "system",
    Tool = "tool",
    Error = "error"
}
/**
 * History item interface
 */
export interface HistoryItem {
    id: string;
    type: MessageType;
    content: string;
    timestamp: Date;
    provider: string | undefined;
    model: string | undefined;
    metadata: Record<string, any> | undefined;
    isStreaming: boolean | undefined;
    isComplete: boolean | undefined;
}
/**
 * Streaming response interface
 */
export interface StreamingResponse {
    content: string;
    isComplete: boolean;
    metadata?: Record<string, any>;
}
/**
 * Provider streaming interface
 */
export interface ProviderStreamer {
    stream(messages: HistoryItem[], onChunk: (chunk: string) => void, onComplete: (response: StreamingResponse) => void, onError: (error: Error) => void, signal?: AbortSignal): Promise<void>;
}
/**
 * Streaming hook
 */
export declare function useStreaming(config: Configuration, logger: Logger): {
    streamingState: StreamingState;
    currentResponse: string;
    error: Error | null;
    submitQuery: (query: string, history: HistoryItem[], onNewMessage: (message: HistoryItem) => void, onUpdateMessage: (id: string, content: string) => void, provider?: string) => Promise<void>;
    cancelStream: () => void;
};
//# sourceMappingURL=useStreaming.d.ts.map