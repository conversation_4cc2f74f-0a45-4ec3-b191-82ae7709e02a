import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React, { useState, useEffect } from 'react';
import { Box, Text, useApp } from 'ink';
import { ChatInterface } from './components/ChatInterface.js';
import { ErrorBoundary } from './components/ErrorBoundary.js';
import { LoadingSpinner } from './components/LoadingSpinner.js';
/**
 * Main App component - Now a single-page chat interface
 */
export function App({ config, logger }) {
    const { exit } = useApp();
    const [state, setState] = useState({
        isLoading: true,
        error: undefined,
        isInitialized: false,
    });
    // Initialize application
    useEffect(() => {
        const initializeApp = async () => {
            try {
                setState(prev => ({ ...prev, isLoading: true, error: undefined }));
                logger.info('🚀 Starting S647 Refactored...');
                // Validate configuration
                if (!config.providers || Object.keys(config.providers).length === 0) {
                    throw new Error('No AI providers configured. Please check your .env file.');
                }
                // Check if default provider is available
                const defaultProvider = config.providers[config.defaultProvider];
                if (!defaultProvider || !defaultProvider.enabled) {
                    const availableProviders = Object.entries(config.providers)
                        .filter(([, provider]) => provider.enabled)
                        .map(([name]) => name);
                    if (availableProviders.length === 0) {
                        throw new Error('No AI providers are enabled. Please configure at least one provider in your .env file.');
                    }
                    logger.warn(`Default provider '${config.defaultProvider}' is not available. Using '${availableProviders[0]}' instead.`);
                }
                // Log configuration summary
                const enabledProviders = Object.entries(config.providers)
                    .filter(([, provider]) => provider.enabled)
                    .map(([name, provider]) => `${name} (${'model' in provider ? provider.model || 'default model' : 'default model'})`)
                    .join(', ');
                logger.info(`✅ Providers: ${enabledProviders}`);
                logger.info(`🔧 Tools: ${config.tools.enabled?.join(', ') || 'none'}`);
                logger.info(`🎨 Theme: ${config.ui.theme}`);
                // Simulate initialization delay for better UX
                await new Promise(resolve => setTimeout(resolve, 1000));
                setState(prev => ({
                    ...prev,
                    isLoading: false,
                    isInitialized: true,
                }));
                logger.info('✨ S647 Refactored initialized successfully!');
            }
            catch (error) {
                logger.error('❌ Failed to initialize S647 Refactored:', error);
                setState(prev => ({
                    ...prev,
                    error: error instanceof Error ? error : new Error(String(error)),
                    isLoading: false,
                    isInitialized: false,
                }));
            }
        };
        initializeApp();
    }, [config, logger]);
    // Handle application exit
    const handleExit = () => {
        logger.info('👋 Goodbye!');
        exit();
    };
    // Show loading state
    if (state.isLoading) {
        return (_jsx(ErrorBoundary, { children: _jsxs(Box, { flexDirection: "column", height: "100%", justifyContent: "center", alignItems: "center", children: [_jsx(LoadingSpinner, {}), _jsx(Box, { marginTop: 2, children: _jsx(Text, { color: "cyan", bold: true, children: "\uD83D\uDE80 Initializing S647 Refactored..." }) }), _jsx(Box, { marginTop: 1, children: _jsx(Text, { color: "gray", dimColor: true, children: "Loading configuration and AI providers" }) }), _jsx(Box, { marginTop: 1, children: _jsx(Text, { color: "gray", dimColor: true, children: "This may take a few seconds..." }) })] }) }));
    }
    // Show error state
    if (state.error) {
        return (_jsx(ErrorBoundary, { children: _jsxs(Box, { flexDirection: "column", height: "100%", justifyContent: "center", alignItems: "center", children: [_jsx(Text, { color: "red", bold: true, children: "\u274C Initialization Error" }), _jsx(Box, { marginTop: 1, marginX: 4, children: _jsx(Text, { color: "red", children: state.error.message }) }), _jsx(Box, { marginTop: 2, children: _jsx(Text, { color: "yellow", children: "\uD83D\uDCA1 Troubleshooting tips:" }) }), _jsx(Box, { marginTop: 1, marginX: 2, children: _jsx(Text, { color: "gray", children: "\u2022 Check your .env file exists and contains valid API keys" }) }), _jsx(Box, { marginX: 2, children: _jsx(Text, { color: "gray", children: "\u2022 Copy .env.example to .env and fill in your API keys" }) }), _jsx(Box, { marginX: 2, children: _jsx(Text, { color: "gray", children: "\u2022 Ensure at least one AI provider is properly configured" }) }), _jsx(Box, { marginTop: 2, children: _jsx(Text, { color: "gray", dimColor: true, children: "Press Ctrl+C to exit" }) })] }) }));
    }
    // Render main chat interface
    return (_jsx(ErrorBoundary, { children: _jsx(ChatInterface, { config: config, logger: logger, onExit: handleExit }) }));
}
//# sourceMappingURL=App.js.map