{"version": 3, "file": "useFileIntegration.js", "sourceRoot": "", "sources": ["../../../src/ui/hooks/useFileIntegration.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AACzD,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,IAAI,EAAE,MAAM,MAAM,CAAC;AA2B5B;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAAC,MAAqB,EAAE,MAAc;IACtE,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAW,EAAE,CAAC,CAAC;IAC7D,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAA6B,IAAI,GAAG,EAAE,CAAC,CAAC;IAClF,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAElD,8BAA8B;IAC9B,MAAM,oBAAoB,GAAG,WAAW,CAAC,GAAa,EAAE;QACtD,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,iBAAiB,IAAI,EAAE,CAAC;QAC9D,OAAO,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACxE,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC;IAE3C,2BAA2B;IAC3B,MAAM,aAAa,GAAG,WAAW,CAAC,CAAC,QAAgB,EAAW,EAAE;QAC9D,MAAM,iBAAiB,GAAG,oBAAoB,EAAE,CAAC;QACjD,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAEhD,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QACjD,OAAO,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACzC,CAAC,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC;IAE3B,sCAAsC;IACtC,MAAM,iBAAiB,GAAG,WAAW,CAAC,CAAC,IAAY,EAAW,EAAE;QAC9D,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,CAAC,eAAe;QACvE,OAAO,IAAI,IAAI,OAAO,CAAC;IACzB,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;IAEjC,mBAAmB;IACnB,MAAM,cAAc,GAAG,WAAW,CAAC,CAAC,QAAgB,EAAE,OAAgB,EAA+B,EAAE;QACrG,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAEjD,cAAc;QACd,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7E,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,mBAAmB;QACnB,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI;YAC/E,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM;YAChF,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7F,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,6BAA6B;QAC7B,IAAI,OAAO,EAAE,CAAC;YACZ,iDAAiD;YACjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBACxD,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;oBACrB,OAAO,QAAQ,CAAC;gBAClB,CAAC;YACH,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,oBAAoB;IACpB,MAAM,eAAe,GAAG,WAAW,CAAC,KAAK,EAAE,QAAgB,EAA0B,EAAE;QACrF,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAE5C,uBAAuB;YACvB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBACjC,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;YACjD,CAAC;YAED,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAExC,uBAAuB;YACvB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,uBAAuB,QAAQ,EAAE,CAAC,CAAC;YACrD,CAAC;YAED,kBAAkB;YAClB,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,KAAK,KAAK,CAAC,IAAI,SAAS,CAAC,CAAC;YACvE,CAAC;YAED,2BAA2B;YAC3B,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,MAAM,IAAI,KAAK,CAAC,0BAA0B,QAAQ,EAAE,CAAC,CAAC;YACxD,CAAC;YAED,oBAAoB;YACpB,MAAM,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAC7C,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAElD,IAAI,OAAe,CAAC;YACpB,IAAI,QAAQ,GAAG,MAAM,CAAC;YAEtB,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;gBACzB,kDAAkD;gBAClD,OAAO,GAAG,gBAAgB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,KAAK,CAAC,IAAI,iBAAiB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC;gBACjH,QAAQ,GAAG,QAAQ,CAAC;YACtB,CAAC;iBAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,qCAAqC;gBACrC,OAAO,GAAG,iBAAiB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,KAAK,CAAC,IAAI,SAAS,CAAC;gBACjF,QAAQ,GAAG,QAAQ,CAAC;YACtB,CAAC;iBAAM,CAAC;gBACN,gCAAgC;gBAChC,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAElC,2BAA2B;gBAC3B,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,gBAAgB;gBACzC,IAAI,OAAO,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;oBAC/B,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,8BAA8B,CAAC;gBAC7E,CAAC;YACH,CAAC;YAED,MAAM,OAAO,GAAkB;gBAC7B,IAAI,EAAE,YAAY;gBAClB,OAAO;gBACP,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,YAAY,EAAE,KAAK,CAAC,KAAK;gBACzB,QAAQ;gBACR,IAAI,EAAE,QAAQ;aACf,CAAC;YAEF,2BAA2B;YAC3B,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC;YAE/D,sBAAsB;YACtB,cAAc,CAAC,IAAI,CAAC,EAAE;gBACpB,MAAM,SAAS,GAAG,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,YAAY,CAAC,CAAC,CAAC;gBAC1E,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,qBAAqB;YACtD,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,CAAC,2BAA2B,QAAQ,EAAE,CAAC,CAAC;YACpD,OAAO,OAAO,CAAC;QAEjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,CAAC,KAAK,CAAC,uBAAuB,QAAQ,GAAG,EAAE,YAAY,CAAC,CAAC;YAE/D,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,EAAE;gBACX,IAAI,EAAE,CAAC;gBACP,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,QAAQ,EAAE,MAAM;gBAChB,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;IACH,CAAC,EAAE,CAAC,aAAa,EAAE,iBAAiB,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC;IAE/D,6CAA6C;IAC7C,MAAM,kBAAkB,GAAG,WAAW,CAAC,KAAK,EAAE,WAAmB,EAA6B,EAAE;QAC9F,IAAI,CAAC;YACH,YAAY,CAAC,IAAI,CAAC,CAAC;YAEnB,MAAM,WAAW,GAAqB,EAAE,CAAC;YACzC,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YAE1B,kEAAkE;YAClE,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,mBAAmB;gBACnB,KAAK,MAAM,UAAU,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;oBACjD,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;wBAC9B,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;wBACtC,WAAW,CAAC,IAAI,CAAC;4BACf,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,UAAU,CAAC;4BACpC,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,KAAK,CAAC,IAAI;4BAChB,YAAY,EAAE,KAAK,CAAC,KAAK;yBAC1B,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;gBAED,wCAAwC;gBACxC,MAAM,WAAW,GAAG,CAAC,cAAc,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;gBACzF,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;oBAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;wBACvE,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;wBACpC,WAAW,CAAC,IAAI,CAAC;4BACf,IAAI,EAAE,IAAI;4BACV,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,KAAK,CAAC,IAAI;4BAChB,YAAY,EAAE,KAAK,CAAC,KAAK;yBAC1B,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;gBAED,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAClC,CAAC;YAED,uBAAuB;YACvB,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;YAClD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAEhD,mCAAmC;YACnC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,OAAO,WAAW,CAAC;YACrB,CAAC;YAED,yBAAyB;YACzB,MAAM,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;YAEnE,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;gBAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBAClD,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;gBAEnD,qCAAqC;gBACrC,IAAI,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,SAAS,KAAK,cAAc,EAAE,CAAC;oBAC9D,SAAS;gBACX,CAAC;gBAED,wCAAwC;gBACxC,IAAI,aAAa,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;oBACpF,SAAS;gBACX,CAAC;gBAED,IAAI,CAAC;oBACH,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;oBAErC,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;wBACxB,WAAW,CAAC,IAAI,CAAC;4BACf,IAAI,EAAE,YAAY,GAAG,GAAG;4BACxB,IAAI,EAAE,WAAW;4BACjB,YAAY,EAAE,KAAK,CAAC,KAAK;yBAC1B,CAAC,CAAC;oBACL,CAAC;yBAAM,IAAI,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;wBAC1B,kCAAkC;wBAClC,IAAI,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC;4BAC7B,WAAW,CAAC,IAAI,CAAC;gCACf,IAAI,EAAE,YAAY;gCAClB,IAAI,EAAE,MAAM;gCACZ,IAAI,EAAE,KAAK,CAAC,IAAI;gCAChB,YAAY,EAAE,KAAK,CAAC,KAAK;6BAC1B,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,2BAA2B;oBAC3B,SAAS;gBACX,CAAC;YACH,CAAC;YAED,oDAAoD;YACpD,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACxB,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;oBACtB,OAAO,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzC,CAAC;gBACD,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;YAEH,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAElC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,EAAE,CAAC;QACZ,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC;IAEzC,oCAAoC;IACpC,MAAM,qBAAqB,GAAG,WAAW,CAAC,KAAK,EAAE,IAAY,EAAmB,EAAE;QAChF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,4BAA4B;QAC5B,MAAM,YAAY,GAAG,aAAa,CAAC;QACnC,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;QAExD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,aAAa,GAAG,IAAI,CAAC;QAEzB,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC5B,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAE1B,IAAI,CAAC,QAAQ;gBAAE,SAAS;YAExB,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,CAAC;gBAEhD,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;oBAClB,6BAA6B;oBAC7B,MAAM,WAAW,GAAG,kBAAkB,QAAQ,KAAK,OAAO,CAAC,KAAK,GAAG,CAAC;oBACpE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;gBAChE,CAAC;qBAAM,CAAC;oBACN,4BAA4B;oBAC5B,MAAM,WAAW,GAAG,iBAAiB,OAAO,CAAC,IAAI,SAAS,OAAO,CAAC,OAAO,gBAAgB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;oBAC/H,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC5E,MAAM,WAAW,GAAG,kBAAkB,QAAQ,KAAK,YAAY,GAAG,CAAC;gBACnE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC,CAAC;IAElD,mBAAmB;IACnB,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE;QAClC,YAAY,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;QACxB,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;IACrC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,uBAAuB;IACvB,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE;QACrC,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACzF,OAAO;YACL,SAAS,EAAE,SAAS,CAAC,IAAI;YACzB,SAAS;YACT,gBAAgB,EAAE,WAAW,CAAC,MAAM;SACrC,CAAC;IACJ,CAAC,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC;IAE7B,OAAO;QACL,eAAe;QACf,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,aAAa;QACb,WAAW;QACX,SAAS;QACT,aAAa;QACb,iBAAiB;KAClB,CAAC;AACJ,CAAC"}