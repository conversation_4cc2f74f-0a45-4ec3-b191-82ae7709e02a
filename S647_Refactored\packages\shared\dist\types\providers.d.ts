/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { ProviderId, JsonValue, AsyncResult } from './common.js';
/**
 * Supported AI provider types
 */
export type ProviderType = 'openai' | 'anthropic' | 'google' | 'mistral' | 'openrouter' | 'custom' | 'local';
/**
 * Provider status
 */
export type ProviderStatus = 'available' | 'unavailable' | 'error' | 'unknown';
/**
 * Model capability flags
 */
export interface ModelCapabilities {
    chat: boolean;
    completion: boolean;
    embedding: boolean;
    vision: boolean;
    functionCalling: boolean;
    streaming: boolean;
    systemMessages: boolean;
    multiModal: boolean;
}
/**
 * Model information
 */
export interface ModelInfo {
    id: string;
    name: string;
    description?: string;
    provider: ProviderType;
    capabilities: ModelCapabilities;
    contextLength: number;
    maxTokens?: number;
    inputCostPer1k?: number;
    outputCostPer1k?: number;
    deprecated?: boolean;
    releaseDate?: string;
}
/**
 * Provider configuration base
 */
export interface BaseProviderConfig {
    type: ProviderType;
    apiKey?: string;
    baseUrl?: string;
    timeout?: number;
    retries?: number;
    headers?: Record<string, string>;
    proxy?: string;
    enabled?: boolean;
}
/**
 * OpenAI provider configuration
 */
export interface OpenAIProviderConfig extends BaseProviderConfig {
    type: 'openai';
    model?: string;
    organization?: string;
    project?: string;
}
/**
 * Anthropic provider configuration
 */
export interface AnthropicProviderConfig extends BaseProviderConfig {
    type: 'anthropic';
    model?: string;
    version?: string;
}
/**
 * Google provider configuration
 */
export interface GoogleProviderConfig extends BaseProviderConfig {
    type: 'google';
    model?: string;
    projectId?: string;
    location?: string;
}
/**
 * Mistral provider configuration
 */
export interface MistralProviderConfig extends BaseProviderConfig {
    type: 'mistral';
    model?: string;
}
/**
 * OpenRouter provider configuration
 */
export interface OpenRouterProviderConfig extends BaseProviderConfig {
    type: 'openrouter';
    model?: string;
    siteName?: string;
    siteUrl?: string;
}
/**
 * Custom provider configuration
 */
export interface CustomProviderConfig extends BaseProviderConfig {
    type: 'custom';
    authType?: 'bearer' | 'api-key' | 'basic' | 'none';
    authHeader?: string;
    models?: ModelInfo[];
}
/**
 * Local provider configuration
 */
export interface LocalProviderConfig extends BaseProviderConfig {
    type: 'local';
    endpoint: string;
    models?: ModelInfo[];
}
/**
 * Union of all provider configurations
 */
export type ProviderConfig = OpenAIProviderConfig | AnthropicProviderConfig | GoogleProviderConfig | MistralProviderConfig | OpenRouterProviderConfig | CustomProviderConfig | LocalProviderConfig;
/**
 * Message role types
 */
export type MessageRole = 'system' | 'user' | 'assistant' | 'function' | 'tool';
/**
 * Content types for messages
 */
export type ContentType = 'text' | 'image' | 'audio' | 'video' | 'file';
/**
 * Message content item
 */
export interface ContentItem {
    type: ContentType;
    data: string | Buffer;
    mimeType?: string;
    metadata?: JsonValue;
}
/**
 * Chat message
 */
export interface ChatMessage {
    id?: string;
    role: MessageRole;
    content: string | ContentItem[];
    name?: string;
    functionCall?: {
        name: string;
        arguments: string;
    };
    toolCalls?: ToolCall[];
    timestamp?: number;
    metadata?: JsonValue;
}
/**
 * Tool call information
 */
export interface ToolCall {
    id: string;
    type: 'function';
    function: {
        name: string;
        arguments: string;
    };
}
/**
 * Function definition for tool calling
 */
export interface FunctionDefinition {
    name: string;
    description?: string;
    parameters?: JsonValue;
}
/**
 * Tool definition
 */
export interface ToolDefinition {
    type: 'function';
    function: FunctionDefinition;
}
/**
 * Chat completion request
 */
export interface ChatCompletionRequest {
    model: string;
    messages: ChatMessage[];
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    stop?: string | string[];
    stream?: boolean;
    tools?: ToolDefinition[];
    toolChoice?: 'auto' | 'none' | {
        type: 'function';
        function: {
            name: string;
        };
    };
    user?: string;
    metadata?: JsonValue;
}
/**
 * Usage statistics
 */
export interface Usage {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
}
/**
 * Chat completion response
 */
export interface ChatCompletionResponse {
    id: string;
    object: 'chat.completion';
    created: number;
    model: string;
    choices: Array<{
        index: number;
        message: ChatMessage;
        finishReason: 'stop' | 'length' | 'function_call' | 'tool_calls' | 'content_filter';
    }>;
    usage?: Usage;
    metadata?: JsonValue;
}
/**
 * Streaming chat completion chunk
 */
export interface ChatCompletionChunk {
    id: string;
    object: 'chat.completion.chunk';
    created: number;
    model: string;
    choices: Array<{
        index: number;
        delta: Partial<ChatMessage>;
        finishReason?: 'stop' | 'length' | 'function_call' | 'tool_calls' | 'content_filter';
    }>;
}
/**
 * Embedding request
 */
export interface EmbeddingRequest {
    model: string;
    input: string | string[];
    user?: string;
    metadata?: JsonValue;
}
/**
 * Embedding response
 */
export interface EmbeddingResponse {
    object: 'list';
    data: Array<{
        object: 'embedding';
        index: number;
        embedding: number[];
    }>;
    model: string;
    usage: {
        promptTokens: number;
        totalTokens: number;
    };
}
/**
 * Provider interface
 */
export interface Provider {
    readonly id: ProviderId;
    readonly type: ProviderType;
    readonly config: ProviderConfig;
    readonly status: ProviderStatus;
    /**
     * Initialize the provider
     */
    initialize(): AsyncResult<void>;
    /**
     * Check if the provider is available
     */
    isAvailable(): Promise<boolean>;
    /**
     * Get available models
     */
    getModels(): AsyncResult<ModelInfo[]>;
    /**
     * Create a chat completion
     */
    createChatCompletion(request: ChatCompletionRequest): AsyncResult<ChatCompletionResponse>;
    /**
     * Create a streaming chat completion
     */
    createChatCompletionStream(request: ChatCompletionRequest): AsyncResult<AsyncIterable<ChatCompletionChunk>>;
    /**
     * Create embeddings
     */
    createEmbedding(request: EmbeddingRequest): AsyncResult<EmbeddingResponse>;
    /**
     * Count tokens for a given input
     */
    countTokens(input: string | ChatMessage[]): AsyncResult<number>;
    /**
     * Dispose of the provider
     */
    dispose(): Promise<void>;
}
/**
 * Provider factory interface
 */
export interface ProviderFactory {
    /**
     * Create a provider instance
     */
    create(config: ProviderConfig): AsyncResult<Provider>;
    /**
     * Get supported provider types
     */
    getSupportedTypes(): ProviderType[];
    /**
     * Validate provider configuration
     */
    validateConfig(config: ProviderConfig): AsyncResult<void>;
}
/**
 * Provider registry interface
 */
export interface ProviderRegistry {
    /**
     * Register a provider
     */
    register(provider: Provider): void;
    /**
     * Unregister a provider
     */
    unregister(id: ProviderId): void;
    /**
     * Get a provider by ID
     */
    get(id: ProviderId): Provider | undefined;
    /**
     * Get all providers
     */
    getAll(): Provider[];
    /**
     * Get providers by type
     */
    getByType(type: ProviderType): Provider[];
    /**
     * Get available providers
     */
    getAvailable(): Provider[];
    /**
     * Find the best provider for a model
     */
    findBestProvider(modelId: string): Provider | undefined;
}
//# sourceMappingURL=providers.d.ts.map