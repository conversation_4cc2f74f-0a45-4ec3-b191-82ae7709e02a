/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { Configuration, Logger } from '@inkbytefo/s647-shared';
import type { HistoryItem } from './useStreaming.js';
/**
 * Memory entry interface
 */
export interface MemoryEntry {
    id: string;
    content: string;
    context: string;
    importance: number;
    timestamp: Date;
    lastAccessed: Date;
    accessCount: number;
    tags: string[];
    source: 'user' | 'ai' | 'system' | 'file';
    metadata?: Record<string, any>;
}
/**
 * Memory search result
 */
export interface MemorySearchResult {
    entry: MemoryEntry;
    relevanceScore: number;
    matchedTags: string[];
    snippet: string;
}
/**
 * Memory statistics
 */
export interface MemoryStats {
    totalEntries: number;
    totalSize: number;
    averageImportance: number;
    mostAccessedEntry: MemoryEntry;
    oldestEntry: MemoryEntry | undefined;
    newestEntry: MemoryEntry | undefined;
    tagDistribution: Record<string, number>;
}
/**
 * Memory manager hook
 */
export declare function useMemory(config: Configuration, logger: Logger): {
    memories: MemoryEntry[];
    addMemory: (content: string, context: string, source?: "user" | "ai" | "system" | "file", metadata?: Record<string, any>) => Promise<string>;
    searchMemories: (query: string, maxResults?: number, minRelevance?: number) => MemorySearchResult[];
    getRelevantMemories: (historyItems: HistoryItem[], maxMemories?: number) => MemoryEntry[];
    removeMemory: (id: string) => boolean;
    clearMemories: () => void;
    getMemoryStats: () => MemoryStats;
    saveMemories: () => Promise<void>;
    loadMemories: () => Promise<void>;
    performCleanup: () => void;
    isLoading: boolean;
};
//# sourceMappingURL=useMemory.d.ts.map