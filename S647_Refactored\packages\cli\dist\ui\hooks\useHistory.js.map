{"version": 3, "file": "useHistory.js", "sourceRoot": "", "sources": ["../../../src/ui/hooks/useHistory.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AA2BzD;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,MAAqB,EAAE,MAAc;IAC9D,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAgB,EAAE,CAAC,CAAC;IAE1D,sBAAsB;IACtB,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,IAAiB,EAAE,EAAE;QAChD,UAAU,CAAC,IAAI,CAAC,EAAE;YAChB,6CAA6C;YAC7C,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;YAE5D,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC;gBACvB,uBAAuB;gBACvB,MAAM,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;gBAC7B,UAAU,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;gBACjC,OAAO,UAAU,CAAC;YACpB,CAAC;iBAAM,CAAC;gBACN,eAAe;gBACf,MAAM,UAAU,GAAG,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,CAAC;gBAEnC,qBAAqB;gBACrB,MAAM,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC,eAAe,IAAI,IAAI,CAAC;gBACnD,IAAI,UAAU,CAAC,MAAM,GAAG,QAAQ,EAAE,CAAC;oBACjC,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC;gBACrC,CAAC;gBAED,OAAO,UAAU,CAAC;YACpB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;IAEhC,uBAAuB;IACvB,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,EAAU,EAAE,OAA6B,EAAE,EAAE;QAC3E,UAAU,CAAC,IAAI,CAAC,EAAE;YAChB,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YACrD,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;gBACf,MAAM,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;gBAC7B,MAAM,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvC,IAAI,YAAY,EAAE,CAAC;oBACjB,UAAU,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,YAAY,EAAE,GAAG,OAAO,EAAE,CAAC;gBACtD,CAAC;gBACD,OAAO,UAAU,CAAC;YACpB,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,2BAA2B;IAC3B,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,EAAU,EAAE,EAAE;QAC5C,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAC1D,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,oBAAoB;IACpB,MAAM,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE;QACpC,UAAU,CAAC,EAAE,CAAC,CAAC;QACf,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;IACtC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,gDAAgD;IAChD,MAAM,mBAAmB,GAAG,WAAW,CAAC,CAAC,KAAa,EAAY,EAAE;QAClE,OAAO,OAAO;aACX,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,MAAqB,CAAC;aACnD,KAAK,CAAC,CAAC,KAAK,CAAC;aACb,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;IAEd,gCAAgC;IAChC,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QACzC,IAAI,CAAC;YACH,MAAM,OAAO,GAAmB;gBAC9B,KAAK,EAAE,OAAO;gBACd,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,OAAO;aACjB,CAAC;YAEF,kEAAkE;YAClE,+CAA+C;YAC/C,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE,CAAC;gBACxC,YAAY,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;gBAC9D,MAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QACjD,CAAC;IACH,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;IAEtB,kCAAkC;IAClC,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QACzC,IAAI,CAAC;YACH,oEAAoE;YACpE,+CAA+C;YAC/C,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE,CAAC;gBACxC,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;gBACpD,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,OAAO,GAAmB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;oBAEnD,iCAAiC;oBACjC,IAAI,OAAO,CAAC,OAAO,KAAK,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;wBAChE,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC1B,MAAM,CAAC,KAAK,CAAC,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,gBAAgB,CAAC,CAAC;oBAC/D,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;oBAC1D,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QACjD,CAAC;IACH,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,iCAAiC;IACjC,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE;YAChC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACvB,WAAW,EAAE,CAAC;YAChB,CAAC;QACH,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,wBAAwB;QAEnC,OAAO,GAAG,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;IAE3B,wBAAwB;IACxB,SAAS,CAAC,GAAG,EAAE;QACb,WAAW,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;IAElB,OAAO;QACL,OAAO;QACP,OAAO;QACP,UAAU;QACV,UAAU;QACV,YAAY;QACZ,mBAAmB;QACnB,WAAW;QACX,WAAW;KACZ,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,IAAiB;IACjD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC;IACtD,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE3C,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;IAC3B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,OAAO,IAAI,IAAI,CAAC;IAClB,CAAC;SAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzB,OAAO,IAAI,SAAS,IAAI,CAAC,QAAQ,GAAG,CAAC;IACvC,CAAC;IAED,OAAO,IAAI,SAAS,KAAK,MAAM,IAAI,OAAO,EAAE,CAAC;AAC/C,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAAC,IAAiB;IAChD,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,MAAqB;YACxB,OAAO,SAAS,CAAC;QACnB,KAAK,WAA0B;YAC7B,OAAO,QAAQ,CAAC;QAClB,KAAK,QAAuB;YAC1B,OAAO,YAAY,CAAC;QACtB,KAAK,MAAqB;YACxB,OAAO,UAAU,CAAC;QACpB,KAAK,OAAsB;YACzB,OAAO,UAAU,CAAC;QACpB;YACE,OAAO,IAAI,CAAC;IAChB,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAAC,IAAiB;IAC/C,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,MAAqB;YACxB,OAAO,MAAM,CAAC;QAChB,KAAK,WAA0B;YAC7B,OAAO,OAAO,CAAC;QACjB,KAAK,QAAuB;YAC1B,OAAO,MAAM,CAAC;QAChB,KAAK,MAAqB;YACxB,OAAO,QAAQ,CAAC;QAClB,KAAK,OAAsB;YACzB,OAAO,KAAK,CAAC;QACf;YACE,OAAO,OAAO,CAAC;IACnB,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,mBAAmB,CAAC,OAAsB,EAAE,IAAiB;IAC3E,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;AACpD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,sBAAsB,CAAC,OAAsB,EAAE,YAAoB,IAAI;IACrF,8CAA8C;IAC9C,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,MAAM,OAAO,GAAkB,EAAE,CAAC;IAElC,wCAAwC;IACxC,KAAK,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QAC7C,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACxB,IAAI,CAAC,IAAI;YAAE,SAAS;QAEpB,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE3D,IAAI,WAAW,GAAG,eAAe,GAAG,SAAS,EAAE,CAAC;YAC9C,MAAM;QACR,CAAC;QAED,sDAAsD;QACtD,IAAI,IAAI,CAAC,IAAI,KAAK,MAAqB,IAAI,IAAI,CAAC,IAAI,KAAK,WAA0B,EAAE,CAAC;YACpF,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtB,WAAW,IAAI,eAAe,CAAC;QACjC,CAAC;IACH,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,mBAAmB,CAAC,OAAsB;IACxD,MAAM,KAAK,GAAG;QACZ,qBAAqB;QACrB,eAAe,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE;QACzC,qBAAqB,OAAO,CAAC,MAAM,EAAE;QACrC,EAAE;KACH,CAAC;IAEF,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACrB,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;QACpC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAAC,OAAsB;IACpD,MAAM,KAAK,GAAG;QACZ,KAAK,EAAE,OAAO,CAAC,MAAM;QACrB,IAAI,EAAE,CAAC;QACP,SAAS,EAAE,CAAC;QACZ,MAAM,EAAE,CAAC;QACT,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,CAAC;QACR,SAAS,EAAE,IAAI,GAAG,EAAU;QAC5B,UAAU,EAAE,CAAC;QACb,eAAe,EAAE,CAAC;KACnB,CAAC;IAEF,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACrB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,MAAqB;gBACxB,KAAK,CAAC,IAAI,EAAE,CAAC;gBACb,MAAM;YACR,KAAK,WAA0B;gBAC7B,KAAK,CAAC,SAAS,EAAE,CAAC;gBAClB,MAAM;YACR,KAAK,QAAuB;gBAC1B,KAAK,CAAC,MAAM,EAAE,CAAC;gBACf,MAAM;YACR,KAAK,MAAqB;gBACxB,KAAK,CAAC,IAAI,EAAE,CAAC;gBACb,MAAM;YACR,KAAK,OAAsB;gBACzB,KAAK,CAAC,KAAK,EAAE,CAAC;gBACd,MAAM;QACV,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrC,CAAC;QAED,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACxC,KAAK,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEH,OAAO;QACL,GAAG,KAAK;QACR,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;KACvC,CAAC;AACJ,CAAC"}