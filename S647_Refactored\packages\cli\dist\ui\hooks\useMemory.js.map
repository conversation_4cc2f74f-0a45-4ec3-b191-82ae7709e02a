{"version": 3, "file": "useMemory.js", "sourceRoot": "", "sources": ["../../../src/ui/hooks/useMemory.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AAsDzD;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,MAAqB,EAAE,MAAc;IAC7D,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAA2B,IAAI,GAAG,EAAE,CAAC,CAAC;IAC9E,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAClD,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAO,IAAI,IAAI,EAAE,CAAC,CAAC;IAEjE,2BAA2B;IAC3B,MAAM,eAAe,GAAG,WAAW,CAAC,GAAiB,EAAE;QACrD,OAAO;YACL,UAAU,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,GAAG;YAC/C,OAAO,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO;YAClC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,IAAI,IAAI,EAAE,SAAS;YAChD,WAAW,EAAE,IAAI;YACjB,oBAAoB,EAAE,IAAI,EAAE,UAAU;SACvC,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAE1B,qBAAqB;IACrB,MAAM,UAAU,GAAG,WAAW,CAAC,GAAW,EAAE;QAC1C,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;IAC5E,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,4BAA4B;IAC5B,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,OAAe,EAAE,OAAe,EAAY,EAAE;QAC7E,MAAM,IAAI,GAAG,IAAI,GAAG,EAAU,CAAC;QAE/B,mBAAmB;QACnB,MAAM,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,cAAc,EAAE,CAAC;YACnB,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAC7D,CAAC;QAED,oCAAoC;QACpC,MAAM,QAAQ,GAAG;YACf,wBAAwB;YACxB,YAAY,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI;YACjE,aAAa;YACb,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ;YACjE,QAAQ;YACR,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK;YACpD,WAAW;YACX,KAAK,EAAE,UAAU,EAAE,gBAAgB,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS;SAC1E,CAAC;QAEF,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAC3C,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAE3C,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACrE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,yBAAyB;QACzB,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACnB,CAAC;QACD,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACpB,CAAC;QACD,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACvB,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,6BAA6B;IAC7B,MAAM,mBAAmB,GAAG,WAAW,CAAC,CAAC,OAAe,EAAE,OAAe,EAAE,MAAc,EAAU,EAAE;QACnG,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC,kBAAkB;QAEtC,iCAAiC;QACjC,IAAI,OAAO,CAAC,MAAM,GAAG,GAAG;YAAE,UAAU,IAAI,CAAC,CAAC;QAC1C,IAAI,OAAO,CAAC,MAAM,GAAG,IAAI;YAAE,UAAU,IAAI,CAAC,CAAC;QAE3C,yBAAyB;QACzB,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,MAAM;gBACT,UAAU,IAAI,CAAC,CAAC,CAAC,0BAA0B;gBAC3C,MAAM;YACR,KAAK,IAAI;gBACP,UAAU,IAAI,CAAC,CAAC,CAAC,wCAAwC;gBACzD,MAAM;YACR,KAAK,MAAM;gBACT,UAAU,IAAI,CAAC,CAAC,CAAC,iCAAiC;gBAClD,MAAM;YACR,KAAK,QAAQ;gBACX,UAAU,IAAI,CAAC,CAAC,CAAC,qCAAqC;gBACtD,MAAM;QACV,CAAC;QAED,2BAA2B;QAC3B,MAAM,iBAAiB,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACvF,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAE3C,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAClC,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACnC,UAAU,IAAI,CAAC,CAAC;YAClB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC,CAAC;IAC/C,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,mBAAmB;IACnB,MAAM,SAAS,GAAG,WAAW,CAAC,KAAK,EACjC,OAAe,EACf,OAAe,EACf,SAA4C,MAAM,EAClD,QAA8B,EACb,EAAE;QACnB,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;YAEvC,6BAA6B;YAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;gBAClC,MAAM,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;gBACjD,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,iBAAiB;YACjB,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC;YACxB,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC3C,MAAM,UAAU,GAAG,mBAAmB,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YACjE,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YAEvB,MAAM,KAAK,GAAgB;gBACzB,EAAE;gBACF,OAAO;gBACP,OAAO;gBACP,UAAU;gBACV,SAAS,EAAE,GAAG;gBACd,YAAY,EAAE,GAAG;gBACjB,WAAW,EAAE,CAAC;gBACd,IAAI;gBACJ,MAAM;gBACN,QAAQ,EAAE,QAAQ,IAAI,EAAE;aACzB,CAAC;YAEF,kBAAkB;YAClB,WAAW,CAAC,IAAI,CAAC,EAAE;gBACjB,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;gBAClC,WAAW,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;gBAE3B,8BAA8B;gBAC9B,IAAI,WAAW,CAAC,IAAI,GAAG,YAAY,CAAC,UAAU,EAAE,CAAC;oBAC/C,4CAA4C;oBAC5C,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;oBACjD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;wBACpB,yDAAyD;wBACzD,IAAI,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,UAAU,EAAE,CAAC;4BAClC,OAAO,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;wBACrC,CAAC;wBACD,OAAO,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;oBAC7D,CAAC,CAAC,CAAC;oBAEH,4BAA4B;oBAC5B,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;oBAC7D,MAAM,MAAM,GAAG,IAAI,GAAG,EAAuB,CAAC;oBAC9C,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;oBACrD,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;oBAEtB,MAAM,CAAC,KAAK,CAAC,2BAA2B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,UAAU,CAAC,CAAC;oBAClF,OAAO,MAAM,CAAC;gBAChB,CAAC;gBAED,OAAO,WAAW,CAAC;YACrB,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,iBAAiB,UAAU,GAAG,CAAC,CAAC;YACtE,OAAO,EAAE,CAAC;QAEZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC7C,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,WAAW,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC,CAAC;IAE1G,kBAAkB;IAClB,MAAM,cAAc,GAAG,WAAW,CAAC,CACjC,KAAa,EACb,aAAqB,EAAE,EACvB,eAAuB,GAAG,EACJ,EAAE;QACxB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;YAAE,OAAO,EAAE,CAAC;QAE7B,MAAM,OAAO,GAAyB,EAAE,CAAC;QACzC,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE3E,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;YACtC,IAAI,cAAc,GAAG,CAAC,CAAC;YACvB,MAAM,WAAW,GAAa,EAAE,CAAC;YAEjC,sBAAsB;YACtB,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YACjD,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAEjD,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACxB,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChC,cAAc,IAAI,GAAG,CAAC;gBACxB,CAAC;gBACD,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChC,cAAc,IAAI,GAAG,CAAC;gBACxB,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,oBAAoB;YACpB,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACvB,IAAI,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;oBAC9C,cAAc,IAAI,GAAG,CAAC;oBACtB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACxB,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,mDAAmD;YACnD,cAAc,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC,CAAC;YAC1C,cAAc,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;YAElD,2BAA2B;YAC3B,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YACnF,cAAc,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,CAAC,CAAC,qBAAqB;YAElE,IAAI,cAAc,IAAI,YAAY,EAAE,CAAC;gBACnC,mBAAmB;gBACnB,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG;oBACxC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;oBACzC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC;gBAElB,OAAO,CAAC,IAAI,CAAC;oBACX,KAAK;oBACL,cAAc;oBACd,WAAW;oBACX,OAAO;iBACR,CAAC,CAAC;gBAEH,wCAAwC;gBACxC,KAAK,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;gBAChC,KAAK,CAAC,WAAW,EAAE,CAAC;YACtB,CAAC;QACH,CAAC;QAED,0BAA0B;QAC1B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC;QAE5D,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,oCAAoC;IACpC,MAAM,mBAAmB,GAAG,WAAW,CAAC,CACtC,YAA2B,EAC3B,cAAsB,CAAC,EACR,EAAE;QACjB,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAEzC,4CAA4C;QAC5C,MAAM,aAAa,GAAG,YAAY;aAC/B,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB;aAC5B,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;aACzB,IAAI,CAAC,GAAG,CAAC,CAAC;QAEb,MAAM,aAAa,GAAG,cAAc,CAAC,aAAa,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;QACtE,OAAO,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnD,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC;IAErB,gBAAgB;IAChB,MAAM,YAAY,GAAG,WAAW,CAAC,CAAC,EAAU,EAAW,EAAE;QACvD,WAAW,CAAC,IAAI,CAAC,EAAE;YACjB,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,OAAO,GAAG,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACvC,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;YAC9C,CAAC;YACD,OAAO,WAAW,CAAC;QACrB,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,qBAAqB;IACrB,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE;QACrC,WAAW,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;QACvB,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;IACtC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,wBAAwB;IACxB,MAAM,cAAc,GAAG,WAAW,CAAC,GAAgB,EAAE;QACnD,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QAE9C,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,MAAM,UAAU,GAAgB;gBAC9B,EAAE,EAAE,EAAE;gBACN,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,EAAE;gBACX,MAAM,EAAE,QAAiB;gBACzB,UAAU,EAAE,CAAC;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,WAAW,EAAE,CAAC;gBACd,IAAI,EAAE,EAAE;aACT,CAAC;YAEF,OAAO;gBACL,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE,CAAC;gBACZ,iBAAiB,EAAE,CAAC;gBACpB,iBAAiB,EAAE,UAAU;gBAC7B,WAAW,EAAE,SAAS;gBACtB,WAAW,EAAE,SAAS;gBACtB,eAAe,EAAE,EAAE;aACpB,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAC9C,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAExD,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CACtD,GAAG,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;QAE9C,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CACtD,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAErD,MAAM,YAAY,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC9C,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;QAEjD,MAAM,eAAe,GAA2B,EAAE,CAAC;QACnD,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACtB,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACvB,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,YAAY,EAAE,OAAO,CAAC,MAAM;YAC5B,SAAS;YACT,iBAAiB;YACjB,iBAAiB;YACjB,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,SAAS;YACzC,WAAW,EAAE,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,SAAS;YAC/D,eAAe;SAChB,CAAC;IACJ,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,4BAA4B;IAC5B,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE;QACtC,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;QACvC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;QAE7D,WAAW,CAAC,IAAI,CAAC,EAAE;YACjB,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;YAClC,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,KAAK,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,WAAW,EAAE,CAAC;gBACtC,0DAA0D;gBAC1D,IAAI,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,UAAU,IAAI,KAAK,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;oBACtE,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBACvB,YAAY,EAAE,CAAC;gBACjB,CAAC;YACH,CAAC;YAED,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;gBACrB,MAAM,CAAC,KAAK,CAAC,wBAAwB,YAAY,eAAe,CAAC,CAAC;YACpE,CAAC;YAED,OAAO,WAAW,CAAC;QACrB,CAAC,CAAC,CAAC;QAEH,cAAc,CAAC,GAAG,CAAC,CAAC;IACtB,CAAC,EAAE,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC;IAE9B,wBAAwB;IACxB,MAAM,YAAY,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QAC1C,IAAI,CAAC;YACH,MAAM,YAAY,GAAG;gBACnB,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;aACxC,CAAC;YAEF,sDAAsD;YACtD,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE,CAAC;gBACxC,YAAY,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;gBACpE,MAAM,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;IAEvB,0BAA0B;IAC1B,MAAM,YAAY,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QAC1C,IAAI,CAAC;YACH,YAAY,CAAC,IAAI,CAAC,CAAC;YAEnB,wDAAwD;YACxD,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE,CAAC;gBACxC,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;gBACrD,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;oBAExC,IAAI,YAAY,CAAC,OAAO,KAAK,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC;wBAC5E,MAAM,cAAc,GAAG,IAAI,GAAG,EAAuB,CAAC;wBAEtD,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,CAAgB,EAAE,EAAE;4BAC1D,4CAA4C;4BAC5C,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;4BAC5C,KAAK,CAAC,YAAY,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;4BAClD,cAAc,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;wBAChC,CAAC,CAAC,CAAC;wBAEH,WAAW,CAAC,cAAc,CAAC,CAAC;wBAC5B,MAAM,CAAC,KAAK,CAAC,UAAU,cAAc,CAAC,IAAI,WAAW,CAAC,CAAC;oBACzD,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,kCAAkC;IAClC,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE;YAChC,IAAI,QAAQ,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBACtB,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,oBAAoB;QAE/B,OAAO,GAAG,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC;IAE7B,4BAA4B;IAC5B,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE;YAChC,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;YAChE,IAAI,oBAAoB,GAAG,MAAM,EAAE,CAAC,CAAC,YAAY;gBAC/C,cAAc,EAAE,CAAC;YACnB,CAAC;QACH,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,wBAAwB;QAEpC,OAAO,GAAG,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC,CAAC;IAElC,yBAAyB;IACzB,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;YACjC,YAAY,EAAE,CAAC;QACjB,CAAC;IACH,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC;IAEjD,OAAO;QACL,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;QACvC,SAAS;QACT,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,SAAS;KACV,CAAC;AACJ,CAAC"}