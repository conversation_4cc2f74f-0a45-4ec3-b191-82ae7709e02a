/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import type { TextBuffer } from './shared/text-buffer.js';
import type { Configuration } from '@inkbytefo/s647-shared';
/**
 * Input prompt props
 */
export interface InputPromptProps {
    buffer: TextBuffer;
    onSubmit: (value: string) => void;
    userMessages: readonly string[];
    onClearScreen: () => void;
    config: Configuration;
    placeholder?: string;
    focus?: boolean;
    inputWidth: number;
    suggestionsWidth: number;
    shellModeActive: boolean;
    setShellModeActive: (value: boolean) => void;
    getCommandSuggestions?: (partial: string) => string[];
}
/**
 * Input prompt component with rich text editing
 */
export declare const InputPrompt: React.FC<InputPromptProps>;
//# sourceMappingURL=InputPrompt.d.ts.map