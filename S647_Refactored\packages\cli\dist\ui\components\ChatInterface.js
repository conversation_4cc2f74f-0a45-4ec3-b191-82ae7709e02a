import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Box, Text, useInput, useStdin, useStdout } from 'ink';
import { useTextBuffer } from './shared/text-buffer.js';
import { InputPrompt } from './InputPrompt.js';
import { useStreaming, StreamingState, MessageType } from '../hooks/useStreaming.js';
import { useHistory, formatHistoryItem, getMessageColor, getMessagePrefix } from '../hooks/useHistory.js';
import { useFileIntegration } from '../hooks/useFileIntegration.js';
import { useMemory } from '../hooks/useMemory.js';
import { useCommands } from '../hooks/useCommands.js';
/**
 * Terminal size hook
 */
function useTerminalSize() {
    const { stdout } = useStdout();
    const [size, setSize] = useState({
        rows: stdout.rows || 24,
        columns: stdout.columns || 80,
    });
    useEffect(() => {
        const updateSize = () => {
            setSize({
                rows: stdout.rows || 24,
                columns: stdout.columns || 80,
            });
        };
        stdout.on('resize', updateSize);
        return () => {
            stdout.off('resize', updateSize);
        };
    }, [stdout]);
    return size;
}
/**
 * Main chat interface component
 */
export const ChatInterface = ({ config, logger, onExit, }) => {
    const { rows: terminalHeight, columns: terminalWidth } = useTerminalSize();
    const { stdin, setRawMode } = useStdin();
    // State
    const [shellModeActive, setShellModeActive] = useState(false);
    const [showHelp, setShowHelp] = useState(false);
    const [currentProvider, setCurrentProvider] = useState(config.defaultProvider);
    const [debugMessage, setDebugMessage] = useState('');
    // Hooks
    const history = useHistory(config, logger);
    const streaming = useStreaming(config, logger);
    const fileIntegration = useFileIntegration(config, logger);
    const memory = useMemory(config, logger);
    const commands = useCommands(config, logger, history.addItem, history.clearHistory, () => history.history, currentProvider, setCurrentProvider);
    // Calculate dimensions
    const inputWidth = Math.max(20, Math.floor(terminalWidth * 0.9) - 3);
    const suggestionsWidth = Math.max(60, Math.floor(terminalWidth * 0.8));
    const chatHeight = Math.max(10, terminalHeight - 15); // Reserve space for input and status
    // Text buffer for input
    const isValidPath = useCallback((filePath) => {
        return fileIntegration.isFileAllowed(filePath);
    }, [fileIntegration]);
    const buffer = useTextBuffer({
        initialText: '',
        viewport: { height: 5, width: inputWidth },
        stdin,
        setRawMode,
        isValidPath,
        shellModeActive,
    });
    // Handle message submission
    const handleSubmit = useCallback(async (message) => {
        if (!message.trim())
            return;
        try {
            // Check if it's a command
            if (message.startsWith('/')) {
                const result = await commands.executeCommand(message);
                if (result) {
                    // Command was executed, result already added to history
                    return;
                }
            }
            // Process file references
            const processedMessage = await fileIntegration.processFileReferences(message);
            // Add to memory
            await memory.addMemory(processedMessage, `User input: ${new Date().toISOString()}`, 'user');
            // Get relevant memories for context
            const relevantMemories = memory.getRelevantMemories(history.history, 3);
            // Submit to streaming
            await streaming.submitQuery(processedMessage, history.history, history.addItem, (id, content) => {
                history.updateItem(id, { content });
            }, currentProvider);
            // Add AI response to memory when complete
            if (streaming.streamingState === StreamingState.Complete) {
                await memory.addMemory(streaming.currentResponse, `AI response: ${new Date().toISOString()}`, 'ai');
            }
        }
        catch (error) {
            logger.error('Failed to submit message:', error);
            const errorMessage = {
                id: `error-${Date.now()}`,
                type: MessageType.Error,
                content: `Error: ${error instanceof Error ? error.message : String(error)}`,
                timestamp: new Date(),
                provider: undefined,
                model: undefined,
                metadata: undefined,
                isStreaming: undefined,
                isComplete: undefined,
            };
            history.addItem(errorMessage);
        }
    }, [commands, fileIntegration, memory, history, streaming, currentProvider, logger]);
    // Handle clear screen
    const handleClearScreen = useCallback(() => {
        history.clearHistory();
        setDebugMessage('Screen cleared');
    }, [history]);
    // Handle global key combinations
    useInput((input, key) => {
        // Handle Ctrl+C for exit
        if (key.ctrl && input === 'c') {
            onExit();
            return;
        }
        // Handle Ctrl+D for exit (if buffer is empty)
        if (key.ctrl && input === 'd' && buffer.text.trim() === '') {
            onExit();
            return;
        }
        // Handle F1 for help
        if (key.f1) {
            setShowHelp(!showHelp);
            return;
        }
        // Handle F2 to toggle shell mode
        if (key.f2) {
            setShellModeActive(!shellModeActive);
            setDebugMessage(`Shell mode ${!shellModeActive ? 'enabled' : 'disabled'}`);
            return;
        }
        // Handle F3 to cycle providers
        if (key.f3) {
            const providers = Object.keys(config.providers).filter(name => config.providers[name]?.enabled);
            const currentIndex = providers.indexOf(currentProvider);
            const nextIndex = (currentIndex + 1) % providers.length;
            const nextProvider = providers[nextIndex];
            if (nextProvider) {
                setCurrentProvider(nextProvider);
                setDebugMessage(`Switched to provider: ${nextProvider}`);
            }
            return;
        }
        // Handle Escape to cancel streaming
        if (key.escape && streaming.streamingState === StreamingState.Streaming) {
            streaming.cancelStream();
            setDebugMessage('Streaming cancelled');
            return;
        }
    }, { isActive: true });
    // Get user messages for history navigation
    const userMessages = useMemo(() => {
        return history.getLastUserMessages(50);
    }, [history]);
    // Render chat messages
    const renderChatMessages = () => {
        const visibleHistory = history.history.slice(-chatHeight);
        return visibleHistory.map((item) => (_jsx(Box, { marginBottom: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsxs(Box, { children: [_jsx(Text, { color: getMessageColor(item.type), bold: true, children: getMessagePrefix(item.type) }), _jsx(Box, { marginLeft: 1, children: _jsx(Text, { color: "gray", dimColor: true, children: item.timestamp.toLocaleTimeString() }) }), item.provider && (_jsx(Box, { marginLeft: 1, children: _jsxs(Text, { color: "gray", dimColor: true, children: ["via ", item.provider] }) })), item.isStreaming && (_jsx(Box, { marginLeft: 1, children: _jsx(Text, { color: "yellow", children: "\u23F3 streaming..." }) }))] }), _jsx(Box, { marginLeft: 2, marginTop: 1, children: _jsx(Text, { color: "white", children: item.content }) })] }) }, item.id)));
    };
    // Render status bar
    const renderStatusBar = () => {
        const memoryStats = memory.getMemoryStats();
        const fileStats = fileIntegration.getCacheStats();
        return (_jsxs(Box, { borderStyle: "single", borderColor: "cyan", padding: 1, children: [_jsxs(Box, { justifyContent: "space-between", width: "100%", children: [_jsx(Box, { children: _jsxs(Text, { color: "cyan", children: ["Provider: ", currentProvider, " | Messages: ", history.history.length, " | Memory: ", memoryStats.totalEntries, " | Files: ", fileStats.fileCount] }) }), _jsx(Box, { children: _jsxs(Text, { color: "cyan", children: [streaming.streamingState === StreamingState.Streaming && '🔄 Streaming', streaming.streamingState === StreamingState.Complete && '✅ Ready', streaming.streamingState === StreamingState.Error && '❌ Error', streaming.streamingState === StreamingState.Idle && '💤 Idle'] }) })] }), debugMessage && (_jsx(Box, { marginTop: 1, children: _jsxs(Text, { color: "yellow", dimColor: true, children: ["Debug: ", debugMessage] }) }))] }));
    };
    // Render help panel
    const renderHelp = () => {
        if (!showHelp)
            return null;
        return (_jsx(Box, { borderStyle: "single", borderColor: "yellow", padding: 1, marginBottom: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: "yellow", bold: true, children: "\uD83C\uDD98 Help & Shortcuts" }), _jsx(Text, { color: "white", children: "\u2022 F1: Toggle this help" }), _jsx(Text, { color: "white", children: "\u2022 F2: Toggle shell mode" }), _jsx(Text, { color: "white", children: "\u2022 F3: Cycle AI providers" }), _jsx(Text, { color: "white", children: "\u2022 Ctrl+C: Exit application" }), _jsx(Text, { color: "white", children: "\u2022 Ctrl+L: Clear screen" }), _jsx(Text, { color: "white", children: "\u2022 Escape: Cancel streaming" }), _jsx(Text, { color: "white", children: "\u2022 @path/to/file: Reference files" }), _jsx(Text, { color: "white", children: "\u2022 \u2191\u2193: Navigate history" }), _jsx(Text, { color: "white", children: "\u2022 Tab: Show suggestions" })] }) }));
    };
    return (_jsxs(Box, { flexDirection: "column", height: "100%", children: [_jsx(Box, { borderStyle: "single", borderColor: "blue", padding: 1, marginBottom: 1, children: _jsx(Box, { justifyContent: "center", width: "100%", children: _jsx(Text, { color: "blue", bold: true, children: "\uD83E\uDD16 S647 Refactored - AI Chat Interface" }) }) }), renderHelp(), _jsx(Box, { flexGrow: 1, borderStyle: "single", borderColor: "gray", padding: 1, marginBottom: 1, flexDirection: "column", children: history.history.length === 0 ? (_jsx(Box, { justifyContent: "center", alignItems: "center", height: "100%", children: _jsxs(Box, { flexDirection: "column", alignItems: "center", children: [_jsx(Text, { color: "gray", bold: true, children: "Welcome to S647 Refactored! \uD83D\uDE80" }), _jsx(Text, { color: "gray", children: "Start chatting with AI or press F1 for help" }), _jsxs(Text, { color: "gray", dimColor: true, children: ["Current provider: ", currentProvider] })] }) })) : (_jsx(Box, { flexDirection: "column", children: renderChatMessages() })) }), _jsx(InputPrompt, { buffer: buffer, onSubmit: handleSubmit, userMessages: userMessages, onClearScreen: handleClearScreen, config: config, placeholder: "\uD83D\uDCAC Type your message, /command, or @path/to/file...", focus: true, inputWidth: inputWidth, suggestionsWidth: suggestionsWidth, shellModeActive: shellModeActive, setShellModeActive: setShellModeActive, getCommandSuggestions: commands.getCommandSuggestions }), renderStatusBar()] }));
};
//# sourceMappingURL=ChatInterface.js.map