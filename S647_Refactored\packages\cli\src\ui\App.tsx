/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import React, { useState, useEffect } from 'react';
import { Box, Text, useApp } from 'ink';
import type { CliArgs } from '../config/args.js';
import type { Configuration } from '../config/types.js';
import type { Logger } from '@inkbytefo/s647-shared';
import { ChatInterface } from './components/ChatInterface.js';
import { ErrorBoundary } from './components/ErrorBoundary.js';
import { LoadingSpinner } from './components/LoadingSpinner.js';

/**
 * App props
 */
export interface AppProps {
  args: CliArgs;
  config: Configuration;
  logger: Logger;
}

/**
 * App state
 */
interface AppState {
  isLoading: boolean;
  error: Error | undefined;
  isInitialized: boolean;
}

/**
 * Main App component - Now a single-page chat interface
 */
export function App({ config, logger }: AppProps): React.JSX.Element {
  const { exit } = useApp();
  const [state, setState] = useState<AppState>({
    isLoading: true,
    error: undefined,
    isInitialized: false,
  });

  // Initialize application
  useEffect(() => {
    const initializeApp = async () => {
      try {
        setState(prev => ({ ...prev, isLoading: true, error: undefined }));

        logger.info('🚀 Starting S647 Refactored...');

        // Validate configuration
        if (!config.providers || Object.keys(config.providers).length === 0) {
          throw new Error('No AI providers configured. Please check your .env file.');
        }

        // Check if default provider is available
        const defaultProvider = config.providers[config.defaultProvider];
        if (!defaultProvider || !defaultProvider.enabled) {
          const availableProviders = Object.entries(config.providers)
            .filter(([, provider]) => provider.enabled)
            .map(([name]) => name);

          if (availableProviders.length === 0) {
            throw new Error('No AI providers are enabled. Please configure at least one provider in your .env file.');
          }

          logger.warn(`Default provider '${config.defaultProvider}' is not available. Using '${availableProviders[0]}' instead.`);
        }

        // Log configuration summary
        const enabledProviders = Object.entries(config.providers)
          .filter(([, provider]) => provider.enabled)
          .map(([name, provider]) => `${name} (${'model' in provider ? provider.model || 'default model' : 'default model'})`)
          .join(', ');

        logger.info(`✅ Providers: ${enabledProviders}`);
        logger.info(`🔧 Tools: ${config.tools.enabled?.join(', ') || 'none'}`);
        logger.info(`🎨 Theme: ${config.ui.theme}`);

        // Simulate initialization delay for better UX
        await new Promise(resolve => setTimeout(resolve, 1000));

        setState(prev => ({
          ...prev,
          isLoading: false,
          isInitialized: true,
        }));

        logger.info('✨ S647 Refactored initialized successfully!');

      } catch (error) {
        logger.error('❌ Failed to initialize S647 Refactored:', error);
        setState(prev => ({
          ...prev,
          error: error instanceof Error ? error : new Error(String(error)),
          isLoading: false,
          isInitialized: false,
        }));
      }
    };

    initializeApp();
  }, [config, logger]);

  // Handle application exit
  const handleExit = () => {
    logger.info('👋 Goodbye!');
    exit();
  };

  // Show loading state
  if (state.isLoading) {
    return (
      <ErrorBoundary>
        <Box flexDirection="column" height="100%" justifyContent="center" alignItems="center">
          <LoadingSpinner />
          <Box marginTop={2}>
            <Text color="cyan" bold>
              🚀 Initializing S647 Refactored...
            </Text>
          </Box>
          <Box marginTop={1}>
            <Text color="gray" dimColor>
              Loading configuration and AI providers
            </Text>
          </Box>
          <Box marginTop={1}>
            <Text color="gray" dimColor>
              This may take a few seconds...
            </Text>
          </Box>
        </Box>
      </ErrorBoundary>
    );
  }

  // Show error state
  if (state.error) {
    return (
      <ErrorBoundary>
        <Box flexDirection="column" height="100%" justifyContent="center" alignItems="center">
          <Text color="red" bold>
            ❌ Initialization Error
          </Text>
          <Box marginTop={1} marginX={4}>
            <Text color="red">
              {state.error.message}
            </Text>
          </Box>
          <Box marginTop={2}>
            <Text color="yellow">
              💡 Troubleshooting tips:
            </Text>
          </Box>
          <Box marginTop={1} marginX={2}>
            <Text color="gray">
              • Check your .env file exists and contains valid API keys
            </Text>
          </Box>
          <Box marginX={2}>
            <Text color="gray">
              • Copy .env.example to .env and fill in your API keys
            </Text>
          </Box>
          <Box marginX={2}>
            <Text color="gray">
              • Ensure at least one AI provider is properly configured
            </Text>
          </Box>
          <Box marginTop={2}>
            <Text color="gray" dimColor>
              Press Ctrl+C to exit
            </Text>
          </Box>
        </Box>
      </ErrorBoundary>
    );
  }

  // Render main chat interface
  return (
    <ErrorBoundary>
      <ChatInterface
        config={config}
        logger={logger}
        onExit={handleExit}
      />
    </ErrorBoundary>
  );
}
