/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { useState, useCallback, useEffect } from 'react';
import type { Configuration, Logger } from '@inkbytefo/s647-shared';
import type { HistoryItem, MessageType } from './useStreaming.js';

/**
 * Command interface
 */
export interface Command {
  name: string;
  description: string;
  usage: string;
  aliases?: string[];
  category: 'system' | 'ai' | 'file' | 'memory' | 'tools';
  handler: (args: string[], context: CommandContext) => Promise<CommandResult>;
}

/**
 * Command context
 */
export interface CommandContext {
  config: Configuration;
  logger: Logger;
  addHistoryItem: (item: HistoryItem) => void;
  clearHistory: () => void;
  getHistory: () => HistoryItem[];
  currentProvider: string;
  setCurrentProvider: (provider: string) => void;
}

/**
 * Command result
 */
export interface CommandResult {
  success: boolean;
  message: string;
  data?: any;
  silent?: boolean; // Don't add to history
}

/**
 * Commands hook
 */
export function useCommands(
  config: Configuration,
  logger: Logger,
  addHistoryItem: (item: HistoryItem) => void,
  clearHistory: () => void,
  getHistory: () => HistoryItem[],
  currentProvider: string,
  setCurrentProvider: (provider: string) => void
) {
  const [commands, setCommands] = useState<Map<string, Command>>(new Map());

  // Create command context
  const context: CommandContext = {
    config,
    logger,
    addHistoryItem,
    clearHistory,
    getHistory,
    currentProvider,
    setCurrentProvider,
  };

  // System commands
  const systemCommands: Command[] = [
    {
      name: 'help',
      description: 'Show available commands',
      usage: '/help [command]',
      aliases: ['h', '?'],
      category: 'system',
      handler: async (args: string[]) => {
        if (args.length > 0) {
          const commandName = args[0];
          if (!commandName) {
            return {
              success: false,
              message: '❌ Command name is required',
            };
          }
          const command = commands.get(commandName);
          if (command) {
            return {
              success: true,
              message: `**${command.name}** - ${command.description}\n\nUsage: ${command.usage}\n\nCategory: ${command.category}${command.aliases ? `\nAliases: ${command.aliases.join(', ')}` : ''}`,
            };
          } else {
            return {
              success: false,
              message: `Command '${commandName}' not found. Use /help to see all commands.`,
            };
          }
        }

        const categories = ['system', 'ai', 'file', 'memory', 'tools'] as const;
        let helpText = '# 🆘 Available Commands\n\n';

        categories.forEach(category => {
          const categoryCommands = Array.from(commands.values()).filter(cmd => cmd.category === category);
          if (categoryCommands.length > 0) {
            helpText += `## ${category.charAt(0).toUpperCase() + category.slice(1)} Commands\n\n`;
            categoryCommands.forEach(cmd => {
              helpText += `- **/${cmd.name}** - ${cmd.description}\n`;
            });
            helpText += '\n';
          }
        });

        helpText += 'Use `/help <command>` for detailed information about a specific command.';

        return {
          success: true,
          message: helpText,
        };
      },
    },
    {
      name: 'clear',
      description: 'Clear chat history',
      usage: '/clear',
      aliases: ['cls'],
      category: 'system',
      handler: async () => {
        clearHistory();
        return {
          success: true,
          message: '🧹 Chat history cleared',
          silent: true,
        };
      },
    },
    {
      name: 'exit',
      description: 'Exit the application',
      usage: '/exit',
      aliases: ['quit', 'q'],
      category: 'system',
      handler: async () => {
        process.exit(0);
      },
    },
    {
      name: 'version',
      description: 'Show version information',
      usage: '/version',
      aliases: ['v'],
      category: 'system',
      handler: async () => {
        return {
          success: true,
          message: `🚀 **S647 Refactored** v2.0.0\n\nBuilt with ❤️ by inkbytefo\nPowered by modern AI providers`,
        };
      },
    },
  ];

  // AI commands
  const aiCommands: Command[] = [
    {
      name: 'providers',
      description: 'List available AI providers',
      usage: '/providers',
      aliases: ['p'],
      category: 'ai',
      handler: async () => {
        const providers = Object.entries(config.providers);
        let message = '🤖 **Available AI Providers:**\n\n';

        providers.forEach(([name, provider]) => {
          const status = provider.enabled ? '✅' : '❌';
          const current = name === currentProvider ? ' **(current)**' : '';
          message += `${status} **${name}**${current}\n`;
          message += `   Model: ${'model' in provider ? provider.model || 'default' : 'default'}\n`;
          message += `   Status: ${provider.enabled ? 'Enabled' : 'Disabled'}\n\n`;
        });

        return {
          success: true,
          message,
        };
      },
    },
    {
      name: 'switch',
      description: 'Switch to a different AI provider',
      usage: '/switch <provider>',
      aliases: ['use'],
      category: 'ai',
      handler: async (args: string[]) => {
        if (args.length === 0) {
          return {
            success: false,
            message: 'Please specify a provider. Use /providers to see available options.',
          };
        }

        const providerName = args[0];
        if (!providerName) {
          return {
            success: false,
            message: '❌ Provider name is required',
          };
        }

        const provider = config.providers[providerName];

        if (!provider) {
          return {
            success: false,
            message: `Provider '${providerName}' not found. Use /providers to see available options.`,
          };
        }

        if (!provider.enabled) {
          return {
            success: false,
            message: `Provider '${providerName}' is not enabled. Please check your configuration.`,
          };
        }

        setCurrentProvider(providerName);
        return {
          success: true,
          message: `🔄 Switched to provider: **${providerName}**`,
        };
      },
    },
  ];

  // File commands
  const fileCommands: Command[] = [
    {
      name: 'files',
      description: 'List recent files',
      usage: '/files',
      aliases: ['f'],
      category: 'file',
      handler: async () => {
        // This would integrate with file integration hook
        return {
          success: true,
          message: '📁 **Recent Files:**\n\nFile integration will be implemented here.',
        };
      },
    },
  ];

  // Memory commands
  const memoryCommands: Command[] = [
    {
      name: 'memory',
      description: 'Show memory statistics',
      usage: '/memory [search]',
      aliases: ['mem'],
      category: 'memory',
      handler: async (args: string[]) => {
        if (args.length > 0) {
          // Search memories
          const query = args.join(' ');
          return {
            success: true,
            message: `🧠 **Memory Search Results for "${query}":**\n\nMemory search will be implemented here.`,
          };
        }

        // Show memory stats
        return {
          success: true,
          message: '🧠 **Memory Statistics:**\n\nMemory stats will be implemented here.',
        };
      },
    },
  ];

  // Tools commands
  const toolsCommands: Command[] = [
    {
      name: 'tools',
      description: 'List available tools',
      usage: '/tools',
      aliases: ['t'],
      category: 'tools',
      handler: async () => {
        const enabledTools = config.tools.enabled || [];
        let message = '🔧 **Available Tools:**\n\n';

        if (enabledTools.length === 0) {
          message += 'No tools are currently enabled.';
        } else {
          enabledTools.forEach(tool => {
            message += `✅ **${tool}**\n`;
          });
        }

        return {
          success: true,
          message,
        };
      },
    },
  ];

  // Initialize commands
  useEffect(() => {
    const allCommands = [
      ...systemCommands,
      ...aiCommands,
      ...fileCommands,
      ...memoryCommands,
      ...toolsCommands,
    ];

    const commandMap = new Map<string, Command>();

    allCommands.forEach(command => {
      // Add main command name
      commandMap.set(command.name, command);
      
      // Add aliases
      if (command.aliases) {
        command.aliases.forEach(alias => {
          commandMap.set(alias, command);
        });
      }
    });

    setCommands(commandMap);
  }, [config, currentProvider]);

  // Parse and execute command
  const executeCommand = useCallback(async (input: string): Promise<CommandResult | null> => {
    if (!input.startsWith('/')) {
      return null; // Not a command
    }

    const parts = input.slice(1).trim().split(/\s+/);
    const commandName = parts[0];
    if (!commandName) {
      return {
        success: false,
        message: '❌ Command name is required',
      };
    }

    const args = parts.slice(1);
    const command = commands.get(commandName);
    if (!command) {
      return {
        success: false,
        message: `❌ Unknown command: /${commandName}\n\nUse /help to see available commands.`,
      };
    }

    try {
      logger.debug(`Executing command: /${commandName}`, { args });
      const result = await command.handler(args, context);
      
      if (!result.silent) {
        // Add command execution to history
        const commandItem: HistoryItem = {
          id: `cmd-${Date.now()}`,
          type: 'system' as MessageType,
          content: `Command: /${commandName} ${args.join(' ')}`,
          timestamp: new Date(),
          provider: undefined,
          model: undefined,
          metadata: undefined,
          isStreaming: undefined,
          isComplete: undefined,
        };
        addHistoryItem(commandItem);

        // Add result to history
        const resultItem: HistoryItem = {
          id: `result-${Date.now()}`,
          type: result.success ? ('system' as MessageType) : ('error' as MessageType),
          content: result.message,
          timestamp: new Date(),
          provider: undefined,
          model: undefined,
          metadata: undefined,
          isStreaming: undefined,
          isComplete: undefined,
        };
        addHistoryItem(resultItem);
      }

      return result;
    } catch (error) {
      logger.error(`Command execution failed: /${commandName}`, error);
      return {
        success: false,
        message: `❌ Command execution failed: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }, [commands, logger, addHistoryItem, context]);

  // Get command suggestions
  const getCommandSuggestions = useCallback((partial: string): string[] => {
    if (!partial.startsWith('/')) {
      return [];
    }

    const query = partial.slice(1).toLowerCase();
    const suggestions: string[] = [];

    for (const [name] of commands) {
      if (name.toLowerCase().startsWith(query)) {
        suggestions.push(`/${name}`);
      }
    }

    return suggestions.slice(0, 10); // Limit to 10 suggestions
  }, [commands]);

  return {
    commands: Array.from(new Set(Array.from(commands.values()))), // Remove duplicates
    executeCommand,
    getCommandSuggestions,
  };
}
