/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { useState, useCallback, useEffect } from 'react';
import type { HistoryItem, MessageType } from './useStreaming.js';
import type { Configuration, Logger } from '@inkbytefo/s647-shared';

/**
 * History manager interface
 */
export interface UseHistoryManagerReturn {
  history: HistoryItem[];
  addItem: (item: HistoryItem) => void;
  updateItem: (id: string, updates: Partial<HistoryItem>) => void;
  removeItem: (id: string) => void;
  clearHistory: () => void;
  getLastUserMessages: (count: number) => string[];
  saveHistory: () => Promise<void>;
  loadHistory: () => Promise<void>;
}

/**
 * History storage interface
 */
interface HistoryStorage {
  items: HistoryItem[];
  lastSaved: string;
  version: string;
}

/**
 * History manager hook
 */
export function useHistory(config: Configuration, logger: Logger): UseHistoryManagerReturn {
  const [history, setHistory] = useState<HistoryItem[]>([]);

  // Add item to history
  const addItem = useCallback((item: HistoryItem) => {
    setHistory(prev => {
      // Check if item already exists (for updates)
      const existingIndex = prev.findIndex(h => h.id === item.id);
      
      if (existingIndex >= 0) {
        // Update existing item
        const newHistory = [...prev];
        newHistory[existingIndex] = item;
        return newHistory;
      } else {
        // Add new item
        const newHistory = [...prev, item];
        
        // Limit history size
        const maxItems = config.ui.maxHistoryLines || 1000;
        if (newHistory.length > maxItems) {
          return newHistory.slice(-maxItems);
        }
        
        return newHistory;
      }
    });
  }, [config.ui.maxHistoryLines]);

  // Update existing item
  const updateItem = useCallback((id: string, updates: Partial<HistoryItem>) => {
    setHistory(prev => {
      const index = prev.findIndex(item => item.id === id);
      if (index >= 0) {
        const newHistory = [...prev];
        const existingItem = newHistory[index];
        if (existingItem) {
          newHistory[index] = { ...existingItem, ...updates };
        }
        return newHistory;
      }
      return prev;
    });
  }, []);

  // Remove item from history
  const removeItem = useCallback((id: string) => {
    setHistory(prev => prev.filter(item => item.id !== id));
  }, []);

  // Clear all history
  const clearHistory = useCallback(() => {
    setHistory([]);
    logger.info('Chat history cleared');
  }, [logger]);

  // Get last user messages for history navigation
  const getLastUserMessages = useCallback((count: number): string[] => {
    return history
      .filter(item => item.type === 'user' as MessageType)
      .slice(-count)
      .map(item => item.content);
  }, [history]);

  // Save history to local storage
  const saveHistory = useCallback(async () => {
    try {
      const storage: HistoryStorage = {
        items: history,
        lastSaved: new Date().toISOString(),
        version: '2.0.0',
      };
      
      // In a real implementation, this would save to a file or database
      // For now, we'll use localStorage if available
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem('s647-history', JSON.stringify(storage));
        logger.debug('History saved to localStorage');
      }
    } catch (error) {
      logger.error('Failed to save history:', error);
    }
  }, [history, logger]);

  // Load history from local storage
  const loadHistory = useCallback(async () => {
    try {
      // In a real implementation, this would load from a file or database
      // For now, we'll use localStorage if available
      if (typeof localStorage !== 'undefined') {
        const stored = localStorage.getItem('s647-history');
        if (stored) {
          const storage: HistoryStorage = JSON.parse(stored);
          
          // Validate version compatibility
          if (storage.version === '2.0.0' && Array.isArray(storage.items)) {
            setHistory(storage.items);
            logger.debug(`Loaded ${storage.items.length} history items`);
          } else {
            logger.warn('History version mismatch, starting fresh');
          }
        }
      }
    } catch (error) {
      logger.error('Failed to load history:', error);
    }
  }, [logger]);

  // Auto-save history periodically
  useEffect(() => {
    const interval = setInterval(() => {
      if (history.length > 0) {
        saveHistory();
      }
    }, 30000); // Save every 30 seconds

    return () => clearInterval(interval);
  }, [history, saveHistory]);

  // Load history on mount
  useEffect(() => {
    loadHistory();
  }, [loadHistory]);

  return {
    history,
    addItem,
    updateItem,
    removeItem,
    clearHistory,
    getLastUserMessages,
    saveHistory,
    loadHistory,
  };
}

/**
 * Format history item for display
 */
export function formatHistoryItem(item: HistoryItem): string {
  const timestamp = item.timestamp.toLocaleTimeString();
  const prefix = getMessagePrefix(item.type);
  
  let content = item.content;
  if (item.isStreaming) {
    content += ' ⏳';
  } else if (item.provider) {
    content += ` (via ${item.provider})`;
  }
  
  return `[${timestamp}] ${prefix} ${content}`;
}

/**
 * Get message prefix based on type
 */
export function getMessagePrefix(type: MessageType): string {
  switch (type) {
    case 'user' as MessageType:
      return '👤 You:';
    case 'assistant' as MessageType:
      return '🤖 AI:';
    case 'system' as MessageType:
      return '⚙️ System:';
    case 'tool' as MessageType:
      return '🔧 Tool:';
    case 'error' as MessageType:
      return '❌ Error:';
    default:
      return '💬';
  }
}

/**
 * Get message color based on type
 */
export function getMessageColor(type: MessageType): string {
  switch (type) {
    case 'user' as MessageType:
      return 'blue';
    case 'assistant' as MessageType:
      return 'green';
    case 'system' as MessageType:
      return 'cyan';
    case 'tool' as MessageType:
      return 'yellow';
    case 'error' as MessageType:
      return 'red';
    default:
      return 'white';
  }
}

/**
 * Filter history by type
 */
export function filterHistoryByType(history: HistoryItem[], type: MessageType): HistoryItem[] {
  return history.filter(item => item.type === type);
}

/**
 * Get conversation context for AI
 */
export function getConversationContext(history: HistoryItem[], maxTokens: number = 4000): HistoryItem[] {
  // Simple token estimation (4 chars ≈ 1 token)
  let totalTokens = 0;
  const context: HistoryItem[] = [];
  
  // Start from the end and work backwards
  for (let i = history.length - 1; i >= 0; i--) {
    const item = history[i];
    if (!item) continue;

    const estimatedTokens = Math.ceil(item.content.length / 4);

    if (totalTokens + estimatedTokens > maxTokens) {
      break;
    }

    // Only include user and assistant messages in context
    if (item.type === 'user' as MessageType || item.type === 'assistant' as MessageType) {
      context.unshift(item);
      totalTokens += estimatedTokens;
    }
  }
  
  return context;
}

/**
 * Export history to text format
 */
export function exportHistoryToText(history: HistoryItem[]): string {
  const lines = [
    '# S647 Chat History',
    `# Exported: ${new Date().toISOString()}`,
    `# Total messages: ${history.length}`,
    '',
  ];
  
  history.forEach(item => {
    lines.push(formatHistoryItem(item));
    lines.push('');
  });
  
  return lines.join('\n');
}

/**
 * Get history statistics
 */
export function getHistoryStats(history: HistoryItem[]) {
  const stats = {
    total: history.length,
    user: 0,
    assistant: 0,
    system: 0,
    tool: 0,
    error: 0,
    providers: new Set<string>(),
    totalChars: 0,
    estimatedTokens: 0,
  };
  
  history.forEach(item => {
    switch (item.type) {
      case 'user' as MessageType:
        stats.user++;
        break;
      case 'assistant' as MessageType:
        stats.assistant++;
        break;
      case 'system' as MessageType:
        stats.system++;
        break;
      case 'tool' as MessageType:
        stats.tool++;
        break;
      case 'error' as MessageType:
        stats.error++;
        break;
    }
    
    if (item.provider) {
      stats.providers.add(item.provider);
    }
    
    stats.totalChars += item.content.length;
    stats.estimatedTokens += Math.ceil(item.content.length / 4);
  });
  
  return {
    ...stats,
    providers: Array.from(stats.providers),
  };
}
