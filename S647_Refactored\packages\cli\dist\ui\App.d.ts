/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import type { CliArgs } from '../config/args.js';
import type { Configuration } from '../config/types.js';
import type { Logger } from '@inkbytefo/s647-shared';
/**
 * App props
 */
export interface AppProps {
    args: CliArgs;
    config: Configuration;
    logger: Logger;
}
/**
 * Main App component - Now a single-page chat interface
 */
export declare function App({ config, logger }: AppProps): React.JSX.Element;
//# sourceMappingURL=App.d.ts.map