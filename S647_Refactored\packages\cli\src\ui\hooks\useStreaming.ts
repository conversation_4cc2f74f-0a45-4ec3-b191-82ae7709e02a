/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { useState, useRef, useCallback, useEffect } from 'react';
import type { Configuration, Logger } from '@inkbytefo/s647-shared';

/**
 * Streaming states
 */
export enum StreamingState {
  Idle = 'idle',
  Connecting = 'connecting',
  Streaming = 'streaming',
  Complete = 'complete',
  Error = 'error',
  UserCancelled = 'cancelled',
}

/**
 * Message types
 */
export enum MessageType {
  User = 'user',
  Assistant = 'assistant',
  System = 'system',
  Tool = 'tool',
  Error = 'error',
}

/**
 * History item interface
 */
export interface HistoryItem {
  id: string;
  type: MessageType;
  content: string;
  timestamp: Date;
  provider: string | undefined;
  model: string | undefined;
  metadata: Record<string, any> | undefined;
  isStreaming: boolean | undefined;
  isComplete: boolean | undefined;
}

/**
 * Streaming response interface
 */
export interface StreamingResponse {
  content: string;
  isComplete: boolean;
  metadata?: Record<string, any>;
}

/**
 * Provider streaming interface
 */
export interface ProviderStreamer {
  stream(
    messages: HistoryItem[],
    onChunk: (chunk: string) => void,
    onComplete: (response: StreamingResponse) => void,
    onError: (error: Error) => void,
    signal?: AbortSignal
  ): Promise<void>;
}

/**
 * OpenAI streaming implementation
 */
class OpenAIStreamer implements ProviderStreamer {
  constructor(
    private apiKey: string,
    private baseUrl: string,
    private model: string,
    private logger: Logger
  ) {}

  async stream(
    messages: HistoryItem[],
    onChunk: (chunk: string) => void,
    onComplete: (response: StreamingResponse) => void,
    onError: (error: Error) => void,
    signal?: AbortSignal
  ): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: this.model,
          messages: messages.map(msg => ({
            role: msg.type === MessageType.User ? 'user' : 'assistant',
            content: msg.content,
          })),
          stream: true,
          temperature: 0.7,
        }),
        signal: signal || null,
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let fullContent = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              onComplete({
                content: fullContent,
                isComplete: true,
                metadata: { provider: 'openai', model: this.model },
              });
              return;
            }

            try {
              const parsed = JSON.parse(data);
              const delta = parsed.choices?.[0]?.delta?.content;
              if (delta) {
                fullContent += delta;
                onChunk(delta);
              }
            } catch (e) {
              // Ignore parsing errors for malformed chunks
            }
          }
        }
      }
    } catch (error) {
      this.logger.error('OpenAI streaming error:', error);
      onError(error instanceof Error ? error : new Error(String(error)));
    }
  }
}

/**
 * Anthropic streaming implementation
 */
class AnthropicStreamer implements ProviderStreamer {
  constructor(
    private apiKey: string,
    private baseUrl: string,
    private model: string,
    private logger: Logger
  ) {}

  async stream(
    messages: HistoryItem[],
    onChunk: (chunk: string) => void,
    onComplete: (response: StreamingResponse) => void,
    onError: (error: Error) => void,
    signal?: AbortSignal
  ): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.apiKey,
          'anthropic-version': '2023-06-01',
        },
        body: JSON.stringify({
          model: this.model,
          messages: messages.map(msg => ({
            role: msg.type === MessageType.User ? 'user' : 'assistant',
            content: msg.content,
          })),
          stream: true,
          max_tokens: 4096,
        }),
        signal: signal || null,
      });

      if (!response.ok) {
        throw new Error(`Anthropic API error: ${response.status} ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let fullContent = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            try {
              const parsed = JSON.parse(data);
              
              if (parsed.type === 'content_block_delta') {
                const delta = parsed.delta?.text;
                if (delta) {
                  fullContent += delta;
                  onChunk(delta);
                }
              } else if (parsed.type === 'message_stop') {
                onComplete({
                  content: fullContent,
                  isComplete: true,
                  metadata: { provider: 'anthropic', model: this.model },
                });
                return;
              }
            } catch (e) {
              // Ignore parsing errors for malformed chunks
            }
          }
        }
      }
    } catch (error) {
      this.logger.error('Anthropic streaming error:', error);
      onError(error instanceof Error ? error : new Error(String(error)));
    }
  }
}

/**
 * Google Gemini streaming implementation
 */
class GeminiStreamer implements ProviderStreamer {
  constructor(
    private apiKey: string,
    private baseUrl: string,
    private model: string,
    private logger: Logger
  ) {}

  async stream(
    messages: HistoryItem[],
    onChunk: (chunk: string) => void,
    onComplete: (response: StreamingResponse) => void,
    onError: (error: Error) => void,
    signal?: AbortSignal
  ): Promise<void> {
    try {
      const contents = messages.map(msg => ({
        role: msg.type === MessageType.User ? 'user' : 'model',
        parts: [{ text: msg.content }],
      }));

      const response = await fetch(
        `${this.baseUrl}/v1beta/models/${this.model}:streamGenerateContent?key=${this.apiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents,
            generationConfig: {
              temperature: 0.7,
              maxOutputTokens: 4096,
            },
          }),
          signal: signal || null,
        }
      );

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let fullContent = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim()) {
            try {
              const parsed = JSON.parse(line);
              const text = parsed.candidates?.[0]?.content?.parts?.[0]?.text;

              if (text) {
                fullContent += text;
                onChunk(text);
              }

              if (parsed.candidates?.[0]?.finishReason) {
                onComplete({
                  content: fullContent,
                  isComplete: true,
                  metadata: { provider: 'google', model: this.model },
                });
                return;
              }
            } catch (e) {
              // Ignore parsing errors for malformed chunks
            }
          }
        }
      }
    } catch (error) {
      this.logger.error('Gemini streaming error:', error);
      onError(error instanceof Error ? error : new Error(String(error)));
    }
  }
}

/**
 * Mistral streaming implementation
 */
class MistralStreamer implements ProviderStreamer {
  constructor(
    private apiKey: string,
    private baseUrl: string,
    private model: string,
    private logger: Logger
  ) {}

  async stream(
    messages: HistoryItem[],
    onChunk: (chunk: string) => void,
    onComplete: (response: StreamingResponse) => void,
    onError: (error: Error) => void,
    signal?: AbortSignal
  ): Promise<void> {
    try {
      const mistralMessages = messages.map(msg => ({
        role: msg.type === MessageType.User ? 'user' : 'assistant',
        content: msg.content,
      }));

      const response = await fetch(`${this.baseUrl}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: this.model,
          messages: mistralMessages,
          stream: true,
          temperature: 0.7,
          max_tokens: 4096,
        }),
        signal: signal || null,
      });

      if (!response.ok) {
        throw new Error(`Mistral API error: ${response.status} ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let fullContent = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              onComplete({
                content: fullContent,
                isComplete: true,
                metadata: { provider: 'mistral', model: this.model },
              });
              return;
            }

            try {
              const parsed = JSON.parse(data);
              const delta = parsed.choices?.[0]?.delta?.content;
              if (delta) {
                fullContent += delta;
                onChunk(delta);
              }
            } catch (e) {
              // Ignore parsing errors for malformed chunks
            }
          }
        }
      }
    } catch (error) {
      this.logger.error('Mistral streaming error:', error);
      onError(error instanceof Error ? error : new Error(String(error)));
    }
  }
}

/**
 * Streaming hook
 */
export function useStreaming(config: Configuration, logger: Logger) {
  const [streamingState, setStreamingState] = useState<StreamingState>(StreamingState.Idle);
  const [currentResponse, setCurrentResponse] = useState<string>('');
  const [error, setError] = useState<Error | null>(null);
  
  const abortControllerRef = useRef<AbortController | null>(null);
  const streamersRef = useRef<Map<string, ProviderStreamer>>(new Map());

  // Initialize streamers
  useEffect(() => {
    const streamers = new Map<string, ProviderStreamer>();
    
    Object.entries(config.providers).forEach(([name, providerConfig]) => {
      if (!providerConfig.enabled || !providerConfig.apiKey) return;
      
      switch (providerConfig.type) {
        case 'openai':
          streamers.set(name, new OpenAIStreamer(
            providerConfig.apiKey,
            providerConfig.baseUrl || 'https://api.openai.com/v1',
            ('model' in providerConfig ? providerConfig.model : undefined) || 'gpt-4',
            logger
          ));
          break;

        case 'anthropic':
          streamers.set(name, new AnthropicStreamer(
            providerConfig.apiKey,
            providerConfig.baseUrl || 'https://api.anthropic.com',
            ('model' in providerConfig ? providerConfig.model : undefined) || 'claude-3-sonnet-20240229',
            logger
          ));
          break;

        case 'google':
          streamers.set(name, new GeminiStreamer(
            providerConfig.apiKey,
            providerConfig.baseUrl || 'https://generativelanguage.googleapis.com',
            ('model' in providerConfig ? providerConfig.model : undefined) || 'gemini-pro',
            logger
          ));
          break;

        case 'mistral':
          streamers.set(name, new MistralStreamer(
            providerConfig.apiKey,
            providerConfig.baseUrl || 'https://api.mistral.ai',
            (providerConfig as any).model || 'mistral-large-latest',
            logger
          ));
          break;
      }
    });
    
    streamersRef.current = streamers;
  }, [config.providers, logger]);

  const submitQuery = useCallback(async (
    query: string,
    history: HistoryItem[],
    onNewMessage: (message: HistoryItem) => void,
    onUpdateMessage: (id: string, content: string) => void,
    provider?: string
  ) => {
    // Cancel any existing stream
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    const abortController = new AbortController();
    abortControllerRef.current = abortController;
    
    // Add user message
    const userMessage: HistoryItem = {
      id: `user-${Date.now()}`,
      type: MessageType.User,
      content: query,
      timestamp: new Date(),
      provider: undefined,
      model: undefined,
      metadata: undefined,
      isStreaming: undefined,
      isComplete: undefined,
    };

    onNewMessage(userMessage);

    // Create assistant message placeholder
    const assistantMessage: HistoryItem = {
      id: `assistant-${Date.now()}`,
      type: MessageType.Assistant,
      content: '',
      timestamp: new Date(),
      provider: undefined,
      model: undefined,
      metadata: undefined,
      isStreaming: true,
      isComplete: false,
    };
    
    onNewMessage(assistantMessage);
    
    try {
      setStreamingState(StreamingState.Connecting);
      setCurrentResponse('');
      setError(null);
      
      const providerName = provider || config.defaultProvider;
      const streamer = streamersRef.current.get(providerName);
      
      if (!streamer) {
        throw new Error(`Provider ${providerName} not available`);
      }
      
      setStreamingState(StreamingState.Streaming);
      
      await streamer.stream(
        [...history, userMessage],
        (chunk: string) => {
          setCurrentResponse(prev => prev + chunk);
          onUpdateMessage(assistantMessage.id, currentResponse + chunk);
        },
        (response: StreamingResponse) => {
          setStreamingState(StreamingState.Complete);
          setCurrentResponse(response.content);
          onUpdateMessage(assistantMessage.id, response.content);
          
          // Mark message as complete
          const completeMessage: HistoryItem = {
            ...assistantMessage,
            content: response.content,
            isStreaming: false,
            isComplete: true,
            provider: response.metadata?.provider,
            model: response.metadata?.model,
            metadata: response.metadata || {},
          };
          
          onNewMessage(completeMessage);
        },
        (streamError: Error) => {
          setStreamingState(StreamingState.Error);
          setError(streamError);
          logger.error('Streaming error:', streamError);
          
          const errorMessage: HistoryItem = {
            id: `error-${Date.now()}`,
            type: MessageType.Error,
            content: `Error: ${streamError.message}`,
            timestamp: new Date(),
            provider: undefined,
            model: undefined,
            metadata: undefined,
            isStreaming: undefined,
            isComplete: undefined,
          };
          
          onNewMessage(errorMessage);
        },
        abortController.signal
      );
    } catch (error) {
      setStreamingState(StreamingState.Error);
      const err = error instanceof Error ? error : new Error(String(error));
      setError(err);
      logger.error('Submit query error:', err);
    }
  }, [config.defaultProvider, currentResponse, logger]);

  const cancelStream = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setStreamingState(StreamingState.UserCancelled);
    }
  }, []);

  return {
    streamingState,
    currentResponse,
    error,
    submitQuery,
    cancelStream,
  };
}
