import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React, { useState, useCallback, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
/**
 * Input prompt component with rich text editing
 */
export const InputPrompt = ({ buffer, onSubmit, userMessages, onClearScreen, config, placeholder = '  Type your message or @path/to/file', focus = true, inputWidth, suggestionsWidth, shellModeActive, setShellModeActive, getCommandSuggestions, }) => {
    const [historyIndex, setHistoryIndex] = useState(-1);
    const [justNavigatedHistory, setJustNavigatedHistory] = useState(false);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [suggestions, setSuggestions] = useState([]);
    // Handle input events
    useInput((input, key) => {
        if (!focus)
            return;
        // Handle special key combinations
        if (key.ctrl) {
            switch (input) {
                case 'c':
                    // Clear current input
                    buffer.setText('');
                    setHistoryIndex(-1);
                    return;
                case 'l':
                    // Clear screen
                    onClearScreen();
                    return;
                case 'd':
                    // Exit if buffer is empty
                    if (buffer.text.trim() === '') {
                        process.exit(0);
                    }
                    return;
                case 'r':
                    // Reverse search (simplified)
                    setShowSuggestions(true);
                    return;
            }
        }
        // Handle Enter key
        if (key.return) {
            const trimmedText = buffer.text.trim();
            if (trimmedText) {
                onSubmit(trimmedText);
                buffer.setText('');
                setHistoryIndex(-1);
                setJustNavigatedHistory(false);
            }
            return;
        }
        // Handle history navigation
        if (key.upArrow && !key.shift) {
            if (userMessages.length > 0) {
                const newIndex = Math.min(historyIndex + 1, userMessages.length - 1);
                if (newIndex !== historyIndex) {
                    setHistoryIndex(newIndex);
                    const historyMessage = userMessages[userMessages.length - 1 - newIndex];
                    if (historyMessage) {
                        buffer.setText(historyMessage);
                        setJustNavigatedHistory(true);
                    }
                }
            }
            return;
        }
        if (key.downArrow && !key.shift) {
            if (historyIndex > 0) {
                const newIndex = historyIndex - 1;
                setHistoryIndex(newIndex);
                const historyMessage = userMessages[userMessages.length - 1 - newIndex];
                if (historyMessage) {
                    buffer.setText(historyMessage);
                    setJustNavigatedHistory(true);
                }
            }
            else if (historyIndex === 0) {
                setHistoryIndex(-1);
                buffer.setText('');
                setJustNavigatedHistory(true);
            }
            return;
        }
        // Handle Tab for suggestions
        if (key.tab) {
            if (showSuggestions && suggestions.length > 0) {
                // Apply first suggestion
                const firstSuggestion = suggestions[0];
                if (firstSuggestion) {
                    buffer.setText(firstSuggestion);
                    setShowSuggestions(false);
                }
            }
            else {
                // Show suggestions
                updateSuggestions(buffer.text);
            }
            return;
        }
        // Handle Escape
        if (key.escape) {
            setShowSuggestions(false);
            return;
        }
        // Reset history navigation flag on any other input
        if (justNavigatedHistory) {
            setJustNavigatedHistory(false);
        }
        // Pass input to text buffer
        buffer.handleInput(input, key);
    }, { isActive: focus });
    // Update suggestions based on current text
    const updateSuggestions = useCallback((text) => {
        const newSuggestions = [];
        // Check for command suggestions
        if (text.startsWith('/') && getCommandSuggestions) {
            const commandSuggestions = getCommandSuggestions(text);
            newSuggestions.push(...commandSuggestions);
        }
        // Check for @file syntax
        else if (text.includes('@')) {
            const atIndex = text.lastIndexOf('@');
            const pathPart = text.slice(atIndex + 1);
            if (pathPart.length > 0) {
                // Simple file suggestions (would be enhanced with actual file system access)
                const commonFiles = [
                    'package.json',
                    'README.md',
                    'src/',
                    'dist/',
                    '.env',
                    'tsconfig.json',
                ];
                newSuggestions.push(...commonFiles
                    .filter(file => file.toLowerCase().includes(pathPart.toLowerCase()))
                    .map(file => text.slice(0, atIndex + 1) + file));
            }
        }
        setSuggestions(newSuggestions.slice(0, 5)); // Limit to 5 suggestions
        setShowSuggestions(newSuggestions.length > 0);
    }, [getCommandSuggestions]);
    // Update suggestions when text changes
    useEffect(() => {
        if (buffer.text && !justNavigatedHistory) {
            updateSuggestions(buffer.text);
        }
        else {
            setShowSuggestions(false);
        }
    }, [buffer.text, justNavigatedHistory, updateSuggestions]);
    // Render input lines
    const renderInputLines = () => {
        const lines = buffer.viewportVisualLines;
        const [cursorRow, cursorCol] = buffer.visualCursor;
        const scrollRow = buffer.visualScrollRow;
        return lines.map((line, index) => {
            const actualRow = scrollRow + index;
            const isCurrentLine = actualRow === cursorRow;
            if (isCurrentLine) {
                // Show cursor on current line
                const beforeCursor = line.slice(0, cursorCol);
                const atCursor = line[cursorCol] || ' ';
                const afterCursor = line.slice(cursorCol + 1);
                return (_jsxs(Box, { children: [_jsx(Text, { children: beforeCursor }), _jsx(Text, { backgroundColor: "white", color: "black", children: atCursor }), _jsx(Text, { children: afterCursor })] }, index));
            }
            return (_jsx(Box, { children: _jsx(Text, { children: line || ' ' }) }, index));
        });
    };
    return (_jsxs(Box, { flexDirection: "column", children: [_jsxs(Box, { borderStyle: "single", borderColor: "blue", padding: 1, width: inputWidth, flexDirection: "column", children: [buffer.text === '' ? (_jsx(Text, { color: "gray", dimColor: true, children: placeholder })) : (_jsx(Box, { flexDirection: "column", children: renderInputLines() })), _jsx(Box, { marginTop: 1, children: _jsxs(Text, { color: "cyan", dimColor: true, children: [shellModeActive ? '🐚 Shell Mode' : '💬 Chat Mode', " | Lines: ", buffer.lines.length, " | Chars: ", buffer.text.length, buffer.text.includes('@') && ' | 📁 File reference detected'] }) })] }), showSuggestions && suggestions.length > 0 && (_jsx(Box, { marginTop: 1, borderStyle: "single", borderColor: "yellow", padding: 1, width: Math.min(suggestionsWidth, inputWidth), children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: "yellow", bold: true, children: "\uD83D\uDCA1 Suggestions (Tab to apply):" }), suggestions.map((suggestion, index) => (_jsx(Box, { marginLeft: 2, children: _jsxs(Text, { color: index === 0 ? 'green' : 'gray', children: [index === 0 ? '→ ' : '  ', suggestion] }) }, index)))] }) })), _jsx(Box, { marginTop: 1, children: _jsx(Text, { color: "gray", dimColor: true, children: "\u2191\u2193 History | Tab Suggestions | Ctrl+C Clear | Ctrl+L Clear Screen | Ctrl+D Exit | @ File Reference | / Commands" }) })] }));
};
//# sourceMappingURL=InputPrompt.js.map